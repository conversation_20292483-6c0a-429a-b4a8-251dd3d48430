# ASP.NET Core 9 Chatbot with Semantic Kernel & Ollama Integration

This is an ASP.NET Core 9 MVC application that provides a real-time chatbot interface using Microsoft Semantic Kernel with local Ollama (Qwen3:14b model) and streaming support via SignalR.

## Features

- **Semantic Kernel Integration**: Uses Microsoft Semantic Kernel for AI orchestration
- **Real-time streaming**: Chat responses are streamed in real-time using SignalR
- **Ollama integration**: Uses local Ollama API with Qwen3:14b model via Semantic Kernel
- **Modern UI**: Bootstrap-based responsive chat interface
- **Connection status**: Shows Ollama and SignalR connection status
- **Error handling**: Graceful error handling and user feedback

## Prerequisites

1. **.NET 9 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
2. **Ollama** - Download and install from [Ollama.ai](https://ollama.ai/)
3. **Qwen3:14b model** - Pull the model using Ollama

## Setup Instructions

### 1. Install and Setup Ollama

```bash
# Install Ollama (if not already installed)
# Download from https://ollama.ai/

# Pull the Qwen3:14b model
ollama pull qwen3:14b

# Start Ollama service (if not running)
ollama serve
```

### 2. Run the Application

```bash
# Navigate to the project directory
cd AspNetChatBot

# Restore dependencies
dotnet restore

# Build the application
dotnet build

# Run the application
dotnet run
```

The application will start on `http://localhost:5000`

## Usage

1. Open your browser and navigate to `http://localhost:5000`
2. The page will automatically redirect to the chat interface
3. Check the status indicators in the top-right corner:
   - **Green "Ollama Connected"**: Ollama is running and accessible
   - **Red "Ollama Disconnected"**: Ollama is not running or not accessible
   - **SignalR status**: Shows connection status to the real-time hub
4. Type your message in the input field and press Enter or click the send button
5. Watch as the AI response streams in real-time via Semantic Kernel

## Architecture

### Components

- **OllamaService**: Handles communication with Ollama via Semantic Kernel
- **ChatHub**: SignalR hub for real-time communication
- **ChatController**: MVC controller for the chat interface
- **JavaScript client**: Handles SignalR connection and UI updates

### Key Files

- `Services/OllamaService.cs` - Semantic Kernel integration with Ollama streaming support
- `Hubs/ChatHub.cs` - SignalR hub for real-time messaging
- `Controllers/ChatController.cs` - MVC controller
- `Views/Chat/Index.cshtml` - Chat interface view
- `wwwroot/js/chat.js` - Client-side JavaScript for SignalR and UI

## Configuration

The application uses the following default settings:

- **Ollama URL**: `http://localhost:11434`
- **Model**: `qwen3:14b`
- **Application Port**: `5000`

To change the model, edit the `DefaultModel` constant in `Services/OllamaService.cs`.

## Troubleshooting

### Ollama Not Connected

1. Ensure Ollama is installed and running: `ollama serve`
2. Verify the model is available: `ollama list`
3. Pull the model if needed: `ollama pull qwen3:14b`
4. Check if Ollama is accessible: `curl http://localhost:11434/api/tags`

### SignalR Connection Issues

1. Check browser console for JavaScript errors
2. Ensure the application is running on the correct port
3. Try refreshing the page to reconnect

### Performance Tips

- The Qwen3:14b model requires significant RAM (8GB+ recommended)
- For better performance, ensure Ollama has access to GPU acceleration if available
- Consider using a smaller model like `qwen3:7b` for faster responses on lower-end hardware

## Development

To modify or extend the application:

1. **Add new models**: Update the `DefaultModel` in `OllamaService.cs`
2. **Customize UI**: Modify `Views/Chat/Index.cshtml` and the CSS in `_Layout.cshtml`
3. **Add features**: Extend the `ChatHub` and corresponding JavaScript client
4. **Configure logging**: Adjust logging levels in `Program.cs`

## License

This project is open source and available under the MIT License.
