﻿using Microsoft.Extensions.VectorData;

namespace ChatbotApp.Models;

// نموذج للبحث في Vector Database
public class ProcedureVector
{
    [VectorStoreKey]
    public string Id { get; set; } = string.Empty; // Primary Key فقط

    [VectorStoreData]
    public string Title { get; set; } = string.Empty;

    [VectorStoreData]
    public string Sector { get; set; } = string.Empty;

    [VectorStoreData]
    public string Department { get; set; } = string.Empty;

    [VectorStoreData]
    public string Section { get; set; } = string.Empty;

    [VectorStoreData]
    public string Description { get; set; } = string.Empty; // النص المُجمع لكل الإجراء

    [VectorStoreData]
    public string SearchableContent { get; set; } = string.Empty; // المحتوى القابل للبحث

    [VectorStoreData]
    public string MetadataJson { get; set; } = string.Empty; // بيانات إضافية كـ JSON

    // Vector للبحث الدلالي
    [VectorStoreVector(768, DistanceFunction = DistanceFunction.CosineSimilarity)]
    public ReadOnlyMemory<float>? ContentEmbedding { get; set; }
}
