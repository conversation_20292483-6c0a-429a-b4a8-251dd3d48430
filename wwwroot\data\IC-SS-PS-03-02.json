{"Key": "63369f63-7771-4692-9ba4-3997d9d71e06", "Id": "IC-SS-PS-03-02", "Sector": "الخدمات المساندة", "Department": "خدمات الأملاك", "Section": "صحة البيئة", "Title": "اصدار او تجديد البطاقات الصحية", "Version": "V.01", "Objective": "وضع قواعد وضوابط وخطوات لإصدار البطاقة الصحية للعاملين بالمحلات ذات العلاقة بالصحة العامة", "Scope": "جميع العاملين في المحلات التجارية ذات العلاقة بالصحة العامة", "Policy": "بناء وتفعيل جهاز رقابي متكامل يستند لى أسس علمية يتسم بالشفافية ويحقق مستويات عالية من سلامة الغذاء وجودته، ويحقق الرقي لجميع المرافق ذات العلاقة بالصحة العامة، وبمشاركة الأطراف ذات العلاقة", "Responsibility": "•\tمدير إدارة خدمات الأملاك\r\n•\tرئيس قسم صحة البيئة \r\n•\tمدرب توعية صحية للمستهلكين \r\n•\tمنسق توعية\r\n•\tمدخل بيانات\r\n•\tالمراكز الطبية المعتمدة", "Definitions": "لا يوجد", "Description": "شرح الآلية النظامية لعملية إصدار تجديد البطاقة الصحية للعاملين بالمرافق ذات العلاقة بالمدينة", "FlowSteps": [{"Step": 1, "Action": "يخضع جميع مناولي الأطعمة والحلاقين والعاملين بالمحلات ذات العلاقة بصحة البيئة للفحص الطبي المحدد وذلك قبل مباشرة العمل في المدن التابعة للهيئة الملكية، وعقب عودتهم من خارج المملكة أو في حالة قيامهم بزيارة المشاعر المقدسة خلال موسم الحج السنوي أو العمرة للتصريح بالعمل في مجال مناولة الأطعمة الصادرة من الهيئة الملكية", "Responsible": "المستفيد", "Duration": "1 Day"}, {"Step": 2, "Action": "إجراء الفحص الطبي في عيادات المراكز الصحية المعتمدة لدى وزارة الصحة والهيئة الملكية", "Responsible": "المراكز الصحية المعتمدة", "Duration": "1 Day"}, {"Step": 3, "Action": "تقديم طلب وتسليم نتائج الفحوصات الطبية والمستندات اللازمة  لإصدار البطاقة الصحية لمختبر ومركز تدريب صحة البيئة في حال كون العامل سليما ، وإلا فيعاد الكشف بعد العلاج مع ابلاغ برنامج الخدمات الصحية", "Responsible": "المستفيد", "Duration": "1 Day"}, {"Step": 4, "Action": "تدقيق اكتمال المستندات ( الفحص الطبي والمتطلبات ) وإصدار الفاتورة", "Responsible": "مدخل بيانات / منسق صحة البيئة", "Duration": "1 Day"}, {"Step": 5, "Action": "اعتماد البطاقة الصحية", "Responsible": "رئيس قسم صحة البيئة", "Duration": "1 Day"}, {"Step": 6, "Action": "اعتماد", "Responsible": "مدير إدارة خدمات الأملاك", "Duration": "1 Day"}, {"Step": 7, "Action": "طباعة البطاقة الصحية وارسالها لمنسق التوعية", "Responsible": "مدخل بيانات", "Duration": "1 Day"}, {"Step": 8, "Action": "في حال كون العامل حاصل على التدريب يسلم البطاقة (انتهى الإجراء) والا فيحال للمدرب لتنسيق دورة تدريب", "Responsible": "مدرب التوعية الصحية للمستهلكين", "Duration": "1 Day"}], "Raci": [{"Task": "إجراء الفحص الطبي", "Responsible": "المراكز الصحية المعتمدة", "Accountable": ""}, {"Task": "تدقيق اكتمال المستندات", "Responsible": "مدخل بيانات", "Accountable": "منسق التوعية"}, {"Task": "إصدار الفاتورة", "Responsible": "مدخل بيانات/منسق صحة البيئة", "Accountable": "رئيس قسم صحة البيئة"}, {"Task": "اعتماد البطاقة الصحية", "Responsible": "رئيس قسم صحة البيئة", "Accountable": "مدير إدارة خدمات الأملاك"}, {"Task": "إصدار البطاقة الصحية", "Responsible": "مدخل بيانات", "Accountable": "منسق التوعية"}], "Kpis": [{"Name": "قياس الطلبات التي تم تلبيتها", "Type": "Percentage", "Value": "100"}], "Forms": [{"Name": "البطاقة الصحية", "Code": "", "Retention": ""}, {"Name": "طلب إصدار البطاقة", "Code": "", "Retention": ""}], "References": ["•\tنظام الصحة العامة\r\n•\tلائحة الغرامات والجزاءات عن المخالفات البلدية للهيئة الملكية"]}