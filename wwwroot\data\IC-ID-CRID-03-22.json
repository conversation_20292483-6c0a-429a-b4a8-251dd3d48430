{"Id": "IC-ID-CRID-03-22", "Sector": "قطاع تطوير الاستثمار", "Department": "إدارة الاستثمار التجاري والسكني", "Section": "قسم الاتفاقيات التجارية والسكنية", "Title": "تنازل عن اتفاقية تأجير وإنهاء إجراء التملك", "Version": "V0.1", "Objective": "التأكد من عدم وجود مخالفات إنشائية ليتم التنازل عن اتفاقية التأجير وإنهاء إجراءات التملك", "Scope": "طلب التنازل عن اتفاقية التأجير", "Policy": "لائحة الاستثمار السكني", "Responsibility": "إدارة الاستثمار التجاري والسكني", "Definitions": "لا يوجد", "Description": "تنازل عن اتفاقية تأجير وإنهاء إجراء التملك", "FlowSteps": [{"Step": 1, "Action": "رسال طلب تنازل عن اتفاقية تأجير المستلم من المستثمر إلى إدارة الاستثمار التجاري والسكني عن طريق إجراء المخاطبات (إجراء معرف مسبقا)", "Responsible": "مدير إدارة علاقات المستثمرين", "Duration": ""}, {"Step": 2, "Action": "استلام طلب تنازل عن اتفاقية تأجير بعد تأكيد إدارة المشاريع بعدم وجود مخالفات إنشائية (بداية الإجراء) (حسب الهيكل التنظيمي)", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 3, "Action": "التحقق من صحة البيانات", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 4, "Action": "التحقق هل يوجد قرض/ ارض، أو هل يوجد طلب سابق", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 5, "Action": "تحديث المعلومات في النظام", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 6, "Action": "التحقق من سداد كامل المستحقات عن طريق إجراء إصدار إشعار دائن (إجراء معرف مسبقا)", "Responsible": "أخصائي إدارة الأملاك", "Duration": ""}, {"Step": 7, "Action": "إصدار اتفاقية التنازل مع التحديث في النظام، يتم استلام ملف المستفيد من إدارة الأملاك في حالة سداد كامل المستحقات فقط", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 8, "Action": "<PERSON>ع<PERSON><PERSON> خطابات (إجراء معرف مسبقا)", "Responsible": "مدير إدارة علاقات المستثمرين", "Duration": ""}, {"Step": 9, "Action": "إرفاق مستندات طلب التنازل مع الاتفاقية", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 10, "Action": "التأييد وإرساله إلى مدير إدارة الاستثمار التجاري والسكني", "Responsible": "رئيس قسم الاتفاقيات التجارية والسكنية", "Duration": ""}, {"Step": 11, "Action": "التأييد وإرساله إلى مدير عام تطوير الاستثمار", "Responsible": "مدير إدارة الاستثمار التجاري والسكني", "Duration": ""}, {"Step": 12, "Action": "التأييد (حس<PERSON> الهيكل التنظيمي)", "Responsible": "مدير عام تطوير الاستثمار", "Duration": ""}, {"Step": 13, "Action": "تحديث البيانات في النظام", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}, {"Step": 14, "Action": "تحديث البيانات في نظام شامل (نهاية الإجراء)", "Responsible": "منسق اتفاقيات (سكني)", "Duration": ""}], "Raci": [{"Task": "التحقق من صحة البيانات", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}, {"Task": "التحقق هل يوجد قرض/ ارض، أو هل يوجد طلب سابق", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}, {"Task": "إصدار اتفاقية التنازل مع التحديث في النظام", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}, {"Task": "إرفاق مستندات طلب التنازل مع الاتفاقية", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}, {"Task": "استلام تأكيد من إدارة المشاريع بعدم وجود مخالفة على المستفيد", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}, {"Task": "التحقق هل يوجد قرض على الجهة الطالبة", "Responsible": "منسق اتفاقيات (سكني)", "Accountable": "رئيس قسم الاتفاقيات التجارية والسكنية"}], "Kpis": [{"Name": "عد<PERSON> التنازلات المتوقع استلامها", "Type": "Count", "Value": "ع<PERSON><PERSON> التنازلات المستلمة خلال شهر"}, {"Name": "عد<PERSON> الأيام المستغرقة لتنفيذ التنازل", "Type": "Count", "Value": "الفترة اللازمة لتنفيذ إجراء التنازل"}, {"Name": "(عدد التنازلات التي تم تلبيتها خلال شهر / مجموع التنازلات المستلمة خلال شهر) * 100%", "Type": "Percentage", "Value": "نسبة التنازلات التي تم استجابتها"}], "Forms": [{"Name": "طلب تنازل عن اتفاقية تأجير", "Code": "", "Retention": ""}], "References": ["لا يوجد"]}