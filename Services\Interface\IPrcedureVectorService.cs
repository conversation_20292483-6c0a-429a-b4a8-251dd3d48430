using ChatbotApp.Models;
using Microsoft.Extensions.VectorData;

namespace ChatbotApp.Services.Interfaces;

public interface IPrcedureVectorService
{
    Task InitializeAsync(string collectionName);
    Task<bool> CreateCollectionIfNotExistsAsync(string collectionName, List<ProcedureVector> data);
    Task<bool> IsAvailableAsync(string collectionName);
    
    Task<IAsyncEnumerable<VectorSearchResult<ProcedureVector>>> SearchAsync(string query, int topK = 5, float minScore = 0.7F);

    //Task<bool> IndexDocumentAsync(T document);
    //Task<List<DocumentSearchResult>> SearchAsync(string query, int topK = 5, float minScore = 0.7f);
    //Task<bool> DeleteDocumentAsync(string documentId);
    //Task<float[]> GenerateEmbeddingAsync(string text);
    //Task<bool> DeleteCollectionAsync(string collectionName);
    //Task<bool> UpsertCollectionAsync(List<T> items, string collectionName);

}