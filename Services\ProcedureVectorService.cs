using ChatbotApp.Models;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using OllamaSharp;
using OllamaSharp; // استيراد المكتبة
using OllamaSharp.Models;
using Qdrant.Client;
using System.Text.Json;
using static OllamaSharp.OllamaApiClient;

namespace ChatbotApp.Services;




public class ProcedureVectorService : IProcedureVectorService
{
    private readonly Kernel _kernel;
    public Kernel Kernel => _kernel;
    private readonly ILogger<ProcedureVectorService> _logger;
    private readonly string _collectionName = "procedures";
    private readonly IWebHostEnvironment _environment;
    private readonly Uri qdrantEndpoint = new Uri("http://localhost:6334");
    //private readonly HttpClient _httpClient;
    private readonly QdrantClient _qdrantClient;

    public ProcedureVectorService(Kernel kernel, ILogger<ProcedureVectorService> logger, IWebHostEnvironment environment)
    {
        
        //_httpClient = new HttpClient();
        _kernel = kernel;
        _logger = logger;
        _environment = environment;        
        _qdrantClient=new QdrantClient(qdrantEndpoint) ;
    }
    //done
    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            var vectorStore = _kernel.Services.GetRequiredService<VectorStore>();
            var embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
            var chatClient = _kernel.GetRequiredService<IChatCompletionService>();

            var proceduresVectors = vectorStore.GetCollection<Guid, ProcedureVector>(_collectionName);
            var collections = vectorStore.ListCollectionNamesAsync();
            var collectionList = new HashSet<string>();
            await foreach (var collection in collections)
            {
                collectionList.Add(collection);
            }

            var collectionExists = collectionList.Contains(_collectionName);
            if (!collectionExists)
            {
                _logger.LogWarning("Qdrant collection '{CollectionName}' does not exist", _collectionName);
                await EnsureCollectionExistsAsync(proceduresVectors);
                return false;
            }


            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Qdrant service not available");
            return false;
        }
    }

    public async Task<List<ProcedureVector>> SearchProceduresAsync(string query, int topK = 5, float minScore = 0.7f)
    {
        var config = new Configuration
        {
            Uri = new Uri("http://localhost:11434"),
            Model = "bge-m3"
        };
        var ollamaClient = new OllamaApiClient(config);
        var queryEmbedding1 = await ollamaClient.GenerateVectorAsync(query,
            new EmbeddingGenerationOptions { 
                ModelId="bge-m3",
                Dimensions = 1024,
                //RawRepresentationFactory = (vector) => vector.ToArray().AsMemory()
                //AdditionalProperties 
            });


        var vectorStore = _kernel.Services.GetRequiredService<VectorStore>();
        //var embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
        var chatClient = _kernel.GetRequiredService<IChatCompletionService>();
        var proceduresVectors = vectorStore.GetCollection<Guid, ProcedureVector>("procedures");
        
        //var queryEmbedding = await embeddingGenerator.GenerateVectorAsync(query);
        //var results = proceduresVectors.SearchAsync(
        //    queryEmbedding,
        //    10,
        //    new VectorSearchOptions<ProcedureVector>
        //    {
        //        VectorProperty = movie => movie.ContentEmbedding
        //    });
        var searchResult =  await _qdrantClient.SearchAsync(_collectionName, queryEmbedding1, 
            limit: (ulong)topK, scoreThreshold: minScore);

        var resultz = new List<ProcedureVector>();
        foreach (var result in searchResult)
        {
            var metadata = result.Payload;

            resultz.Add(
                new ProcedureVector
                {

                    Id = metadata.TryGetValue("Id", out var id) ? id.StringValue : "",
                    Title = metadata.TryGetValue("Title", out var title) ? title.StringValue : "",
                    Sector = metadata.TryGetValue("Sector", out var sector) ? sector.StringValue : "",
                    Department = metadata.TryGetValue("Department", out var department) ? department.StringValue : "",
                    Section = metadata.TryGetValue("Section", out var section) ? section.StringValue : "",
                    Description = metadata.TryGetValue("Description", out var description) ? description.StringValue : "",
                    SearchableContent = metadata.TryGetValue("SearchableContent", out var searchableContent) ? searchableContent.StringValue : "",
                    MetadataJson = metadata.TryGetValue("MetadataJson", out var metadataJson) ? metadataJson.StringValue : "",
                    //MetadataJson = metadata.ToDictionary(
                    //    kvp => kvp.Key,
                    //    kvp => (object)kvp.Value.StringValue
                    //)
                }
                
            );
        }
        return resultz;
    }
    private async Task<List<ProcedureSearchResult>> SearchProceduresAsync1(string query, int topK = 5, float minScore = 0.7f)
    {
        try
        {
            if (!await IsAvailableAsync())
            {
                return new List<ProcedureSearchResult>();
            }
            var embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
            // Generate embedding for query
            var queryEmbedding = await embeddingGenerator.GenerateVectorAsync(query);
            //if (queryEmbedding == null)
            //{
            //    _logger.LogError("Failed to generate embedding for search query");
            //    return new List<ProcedureSearchResult>();
            //}

            // Search in Qdrant
            var searchResult = await _qdrantClient.SearchAsync(_collectionName, queryEmbedding, 
                limit: (ulong)topK, scoreThreshold: minScore);

            var results = new List<ProcedureSearchResult>();
            
            foreach (var point in searchResult)
            {
                var metadata = point.Payload;
                var result = new ProcedureSearchResult
                {
                    Id = metadata.TryGetValue("id", out var id) ? id.StringValue : "",
                    Title = metadata.TryGetValue("title", out var title) ? title.StringValue : "",
                    Content = metadata.TryGetValue("content", out var content) ? content.StringValue : "",
                    Score = point.Score,
                    Metadata = metadata.ToDictionary(
                        kvp => kvp.Key, 
                        kvp => (object)kvp.Value.StringValue
                    )
                };
                results.Add(result);
            }

            _logger.LogInformation("Found {ResultCount} procedures for query: {Query}", results.Count, query);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching procedures for query: {Query}", query);
            return new List<ProcedureSearchResult>();
        }
    }

    //Done
    private async Task EnsureCollectionExistsAsync(VectorStoreCollection<Guid, ProcedureVector> vectorStore)
    {
        try
        {
            var embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();

            await vectorStore.EnsureCollectionExistsAsync();

            var procedures = GetAllProcedures();
            var procedureVectors = procedures.Select(p => p.ToVectorItem()).ToList();
            var tasks = procedureVectors.Select(entry => Task.Run(async ()=>
            {
                entry.ContentEmbedding = (await embeddingGenerator.GenerateAsync(entry.SearchableContent)).Vector;
                
            }));
            await Task.WhenAll(tasks);
            await vectorStore.UpsertAsync(procedureVectors);            
            _logger.LogInformation("Created Qdrant collection: {CollectionName}", _collectionName);
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring collection exists");
            throw;
        }
    }


    private List<Procedure> GetAllProcedures()
    {
        var procedures = new List<Procedure>();
        var dataPath = Path.Combine(_environment.WebRootPath, "data");

        try
        {
            if (Directory.Exists(dataPath))
            {
                var jsonFiles = Directory.GetFiles(dataPath, "*.json");

                foreach (var file in jsonFiles)
                {
                    try
                    {
                        var json = System.IO.File.ReadAllText(file);
                        var procedure = JsonSerializer.Deserialize<Procedure>(json);
                        if (procedure != null)
                        {
                            procedures.Add(procedure);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error reading procedure file {File}", file);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading procedures");
        }

        return procedures.OrderBy(p => p.Id).ToList();
    }

    public Task<bool> IndexProcedureAsync(ProcedureVectorItem procedure)
    {
        throw new NotImplementedException();
    }

    public Task<bool> IndexAllProceduresAsync()
    {
        throw new NotImplementedException();
    }

    public Task<bool> DeleteProcedureAsync(string procedureId)
    {
        throw new NotImplementedException();
    }

    public async Task<List<ProcedureVector>> SearchProceduresAsync11(string query, int topK = 5, float minScore = 0.7f)
    {
        var vectorStore = _kernel.Services.GetRequiredService<VectorStore>();
        var embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
        var proceduresVectors = vectorStore.GetCollection<Guid, ProcedureVector>("procedures");

        var queryEmbedding = await embeddingGenerator.GenerateVectorAsync(query);

        _logger.LogInformation("Searching with query: {Query}, TopK: {TopK}, MinScore: {MinScore}", query, topK, minScore,_kernel);

        //var searchResult = await _qdrantClient.SearchAsync(_collectionName, queryEmbedding,
        //    limit: (ulong)topK, scoreThreshold: minScore);

        var searchResult =  proceduresVectors.SearchAsync(queryEmbedding, 10,
            new VectorSearchOptions<ProcedureVector>
            {
                VectorProperty = pro => pro.ContentEmbedding,
                
            });

        

        var results = new List<ProcedureVector>();
        int count = 1;
        await foreach (var item in searchResult)
        {
            var record = item.Record;
            try
            {
                _logger.LogInformation("Processing result {Index}: ID={Id}, Score={Score}", count, item.Record.Id, item.Score);
                results.Add(new ProcedureVector
                {
                    Id = record.Id,
                    Title = record.Title,
                    Sector = record.Sector,
                    Department = record.Department,
                    Section = record.Section,
                    Description = record.Description,
                    SearchableContent = record.SearchableContent,
                    MetadataJson = record.MetadataJson,
                    ContentEmbedding = record.ContentEmbedding?.ToArray().AsMemory()
                });
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, "Error processing search result {Index}", count);
            }
            
            count++;
        }
       

        _logger.LogInformation("Returning {Count} processed results", results.Count);
        return results;
    }

    private string GetPayloadValue(IDictionary<string, Qdrant.Client.Grpc.Value> payload, string key)
    {
        if (payload?.TryGetValue(key, out var value) == true)
        {
            return GetPayloadValueAsString(value);
        }
        return null;
    }

    private string GetPayloadValueAsString(Qdrant.Client.Grpc.Value value)
    {
        if (value == null) return "";

        return value.KindCase switch
        {
            Qdrant.Client.Grpc.Value.KindOneofCase.StringValue => value.StringValue,
            Qdrant.Client.Grpc.Value.KindOneofCase.IntegerValue => value.IntegerValue.ToString(),
            Qdrant.Client.Grpc.Value.KindOneofCase.DoubleValue => value.DoubleValue.ToString(),
            Qdrant.Client.Grpc.Value.KindOneofCase.BoolValue => value.BoolValue.ToString(),
            _ => ""
        };
    }



    /*
    private string CreateProcedureContent(ProcedureVectorItem procedure)
    {
        var content = new List<string>
        {
            $"Procedure ID: {procedure.Id}",
            $"Title: {procedure.Payload.Title}",
            $"Sector: {procedure.Payload.Sector}",
            $"Department: {procedure.Payload.Department}",
            $"Section: {procedure.Payload.Section}",
            $"Version: {procedure.Payload.Version}",
            $"Objective: {procedure.Payload.Objective}",
            $"Scope: {procedure.Payload.Scope}",
            $"Policy: {procedure.Payload.Policy}",
            $"Responsibility: {procedure.Payload.Responsibility}",
            $"Definitions: {procedure.Payload.Definitions}",
            $"Description: {procedure.Payload.Description}"
        };

        // Add flow steps
        if (procedure.Payload.FlowSteps.Any())
        {
            content.Add("Flow Steps:");
            foreach (var step in procedure.Payload.FlowSteps)
            {
                content.Add($"Step {step.Step}: {step.Action} (Responsible: {step.Responsible}, Duration: {step.Duration})");
            }
        }

        // Add RACI matrix
        if (procedure.Payload.Raci.Any())
        {
            content.Add("RACI Matrix:");
            foreach (var raci in procedure.Payload.Raci)
            {
                content.Add($"Task: {raci.Task} (Responsible: {raci.Responsible}, Accountable: {raci.Accountable})");
            }
        }

        // Add KPIs
        if (procedure.Payload.Kpis.Any())
        {
            content.Add("KPIs:");
            foreach (var kpi in procedure.Payload.Kpis)
            {
                content.Add($"KPI: {kpi.Name} ({kpi.Type}) - Target: {kpi.Value}");
            }
        }

        // Add forms
        if (procedure.Payload.Forms.Any())
        {
            content.Add("Related Forms:");
            foreach (var form in procedure.Payload.Forms)
            {
                content.Add($"Form: {form.Name} (Code: {form.Code}, Retention: {form.Retention})");
            }
        }

        // Add references
        if (procedure.Payload.References.Any())
        {
            content.Add("References:");
            content.AddRange(procedure.Payload.References);
        }

        return string.Join("\n", content.Where(c => !string.IsNullOrWhiteSpace(c)));
    }

    private async Task<float[]?> GenerateEmbeddingAsync(string text)
    {
        try
        {

            await movies.EnsureCollectionExistsAsync();

            var movieData = MovieDatabase.GetMovies();

            var tasks = movieData.Select(entry => Task.Run(async () =>
            {
                entry.DescriptionEmbedding = (await embeddingGenerator.GenerateAsync(entry.Description)).Vector;
            }));
            await Task.WhenAll(tasks);

            await movies.UpsertAsync(movieData);

            var request = new
            {
                model = "nomic-embed-text",
                prompt = text
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("http://localhost:11434/api/embeddings", content);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Ollama embedding API returned {StatusCode}", response.StatusCode);
                return null;
            }

            var responseJson = await response.Content.ReadAsStringAsync();
            var embeddingResponse = JsonSerializer.Deserialize<JsonElement>(responseJson);

            if (embeddingResponse.TryGetProperty("embedding", out var embeddingArray))
            {
                var embedding = embeddingArray.EnumerateArray()
                    .Select(x => x.GetSingle())
                    .ToArray();

                _logger.LogDebug("Generated embedding with {Dimensions} dimensions", embedding.Length);
                return embedding;
            }

            _logger.LogError("Invalid response format from Ollama embedding API");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding via Ollama API");
            return null;
        }
    }

    public Task<bool> IndexProcedureAsync(ProcedureVectorItem procedure)
    {
        throw new NotImplementedException();
    }
    public async Task<bool> DeleteProcedureAsync(string procedureId)
    {
        try
        {
            // Search for points with this procedure ID
            var filter = new Filter
            {
                Must = {
                    new Condition
                    {
                        Field = new FieldCondition
                        {
                            Key = "id",
                            Match = new Match { Text = procedureId }
                        }
                    }
                }
            };

            var searchResult = await _qdrantClient.ScrollAsync(_collectionName, filter: filter);

            if (searchResult.Result.Any())
            {
                var pointIds = searchResult.Result.Select(p => p.Id).ToArray();
                await _qdrantClient.DeleteAsync(_collectionName, pointIds);
                _logger.LogInformation("Deleted {Count} points for procedure {ProcedureId}", pointIds.Length, procedureId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting procedure {ProcedureId}", procedureId);
            return false;
        }
    }
    
    public async Task<bool> IndexProcedureAsync(List<Procedure> procedures)
    {
        try
        {
            await EnsureCollectionExistsAsync();

            // Create comprehensive text content for embedding
            //var content = CreateProcedureContent(procedure);
            var vectorItems = procedures.Select(p => p.ToVectorItem()).ToList();

            // Generate embedding using direct Ollama API call
            var embedding = await GenerateEmbeddingAsync(vectorItems);
            if (embedding == null)
            {
                _logger.LogError("Failed to generate embedding for procedure {ProcedureId}", procedure.Id);
                return false;
            }

            // Create metadata
            var metadata = new Dictionary<string, Value>
            {
                ["id"] = procedure.Id,
                ["title"] = procedure.Payload.Title,
                ["sector"] = procedure.Payload.Sector,
                ["department"] = procedure.Payload.Department,
                ["section"] = procedure.Payload.Section ?? "",
                ["version"] = procedure.Payload.Version,
                ["content"] = content,
                ["indexed_at"] = DateTimeOffset.UtcNow.ToString("O")
            };

            // Create point
            var point = new PointStruct
            {
                Id = new PointId { Uuid = Guid.NewGuid().ToString() },
                Vectors = embedding,
                Payload = { metadata }
            };

            // Upsert point
            await _qdrantClient.UpsertAsync(_collectionName, new[] { point });

            _logger.LogInformation("Successfully indexed procedure {ProcedureId}", procedure.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing procedure {ProcedureId}", procedure.Id);
            return false;
        }
    }

    public async Task<bool> IndexAllProceduresAsync()
    {
        try
        {
            var dataPath = Path.Combine(_environment.WebRootPath, "data");
            if (!Directory.Exists(dataPath))
            {
                _logger.LogWarning("Data directory not found: {DataPath}", dataPath);
                return false;
            }

            var jsonFiles = Directory.GetFiles(dataPath, "*.json");
            var successCount = 0;
            var totalCount = jsonFiles.Length;
            List<Procedure> procedures = new List<Procedure>();
            foreach (var file in jsonFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var procedure = JsonSerializer.Deserialize<Procedure>(json);
                    
                    if (procedure != null)
                    {
                        //var success = await IndexProcedureAsync(procedure);
                        procedures.Add(procedure);
                        //if (success) successCount++;
                        successCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing file {File}", file);
                }
            }
            await IndexProcedureAsync(procedures);

            _logger.LogInformation("Indexed {SuccessCount}/{TotalCount} procedures", successCount, totalCount);
            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing all procedures");
            return false;
        }
    }

    */
}
