using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using System.Text.Json;

namespace ChatbotApp.Services;

using ChatbotApp.Models;

public class ProcedureVectorService : IProcedureVectorService
{
    private readonly QdrantClient _qdrantClient;
    private readonly ITextEmbeddingGenerationService _embeddingService;
    private readonly ILogger<ProcedureVectorService> _logger;
    private readonly string _collectionName = "procedures";
    private readonly IWebHostEnvironment _environment;

    public ProcedureVectorService(
        QdrantClient qdrantClient,
        ITextEmbeddingGenerationService embeddingService,
        ILogger<ProcedureVectorService> logger,
        IWebHostEnvironment environment)
    {
        _qdrantClient = qdrantClient;
        _embeddingService = embeddingService;
        _logger = logger;
        _environment = environment;
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            await _qdrantClient.ListCollectionsAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Qdrant service not available");
            return false;
        }
    }

    public async Task<bool> IndexProcedureAsync(ProcedureVectorItem procedure)
    {
        try
        {
            await EnsureCollectionExistsAsync();

            // Create comprehensive text content for embedding
            var content = CreateProcedureContent(procedure);
            
            // Generate embedding
            var embedding = await _embeddingService.GenerateEmbeddingAsync(content);
            
            // Create metadata
            var metadata = new Dictionary<string, Value>
            {
                ["id"] = procedure.Id,
                ["title"] = procedure.Payload.Title,
                ["sector"] = procedure.Payload.Sector,
                ["department"] = procedure.Payload.Department,
                ["section"] = procedure.Payload.Section ?? "",
                ["version"] = procedure.Payload.Version,
                ["content"] = content,
                ["indexed_at"] = DateTimeOffset.UtcNow.ToString("O")
            };

            // Create point
            var point = new PointStruct
            {
                Id = new PointId { Uuid = Guid.NewGuid().ToString() },
                Vectors = embedding.ToArray(),
                Payload = { metadata }
            };

            // Upsert point
            await _qdrantClient.UpsertAsync(_collectionName, new[] { point });
            
            _logger.LogInformation("Successfully indexed procedure {ProcedureId}", procedure.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing procedure {ProcedureId}", procedure.Id);
            return false;
        }
    }

    public async Task<bool> IndexAllProceduresAsync()
    {
        try
        {
            var dataPath = Path.Combine(_environment.WebRootPath, "data");
            if (!Directory.Exists(dataPath))
            {
                _logger.LogWarning("Data directory not found: {DataPath}", dataPath);
                return false;
            }

            var jsonFiles = Directory.GetFiles(dataPath, "*.json");
            var successCount = 0;
            var totalCount = jsonFiles.Length;

            foreach (var file in jsonFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var procedure = JsonSerializer.Deserialize<ProcedureVectorItem>(json);
                    
                    if (procedure != null)
                    {
                        var success = await IndexProcedureAsync(procedure);
                        if (success) successCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing file {File}", file);
                }
            }

            _logger.LogInformation("Indexed {SuccessCount}/{TotalCount} procedures", successCount, totalCount);
            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing all procedures");
            return false;
        }
    }

    public async Task<List<ProcedureSearchResult>> SearchProceduresAsync(string query, int topK = 5, float minScore = 0.7f)
    {
        try
        {
            if (!await IsAvailableAsync())
            {
                return new List<ProcedureSearchResult>();
            }

            // Generate embedding for query
            var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query);

            // Search in Qdrant
            var searchResult = await _qdrantClient.SearchAsync(_collectionName, queryEmbedding.ToArray(), limit: (ulong)topK, scoreThreshold: minScore);

            var results = new List<ProcedureSearchResult>();
            
            foreach (var point in searchResult)
            {
                var metadata = point.Payload;
                var result = new ProcedureSearchResult
                {
                    Id = metadata.TryGetValue("id", out var id) ? id.StringValue : "",
                    Title = metadata.TryGetValue("title", out var title) ? title.StringValue : "",
                    Content = metadata.TryGetValue("content", out var content) ? content.StringValue : "",
                    Score = point.Score,
                    Metadata = metadata.ToDictionary(
                        kvp => kvp.Key, 
                        kvp => (object)kvp.Value.StringValue
                    )
                };
                results.Add(result);
            }

            _logger.LogInformation("Found {ResultCount} procedures for query: {Query}", results.Count, query);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching procedures for query: {Query}", query);
            return new List<ProcedureSearchResult>();
        }
    }

    public async Task<bool> DeleteProcedureAsync(string procedureId)
    {
        try
        {
            // Search for points with this procedure ID
            var filter = new Filter
            {
                Must = {
                    new Condition
                    {
                        Field = new FieldCondition
                        {
                            Key = "id",
                            Match = new Match { Text = procedureId }
                        }
                    }
                }
            };

            var searchResult = await _qdrantClient.ScrollAsync(_collectionName, filter: filter);

            if (searchResult.Result.Any())
            {
                var pointIds = searchResult.Result.Select(p => p.Id).ToArray();
                await _qdrantClient.DeleteAsync(_collectionName, pointIds);
                _logger.LogInformation("Deleted {Count} points for procedure {ProcedureId}", pointIds.Length, procedureId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting procedure {ProcedureId}", procedureId);
            return false;
        }
    }

    private async Task EnsureCollectionExistsAsync()
    {
        try
        {
            var collections = await _qdrantClient.ListCollectionsAsync();
            var collectionExists = collections.Any(c => c == _collectionName);

            if (!collectionExists)
            {
                var vectorParams = new VectorParams
                {
                    Size = 768, // nomic-embed-text embedding size
                    Distance = Distance.Cosine
                };

                await _qdrantClient.CreateCollectionAsync(_collectionName, vectorParams);
                _logger.LogInformation("Created Qdrant collection: {CollectionName}", _collectionName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring collection exists");
            throw;
        }
    }

    private string CreateProcedureContent(ProcedureVectorItem procedure)
    {
        var content = new List<string>
        {
            $"Procedure ID: {procedure.Id}",
            $"Title: {procedure.Payload.Title}",
            $"Sector: {procedure.Payload.Sector}",
            $"Department: {procedure.Payload.Department}",
            $"Section: {procedure.Payload.Section}",
            $"Version: {procedure.Payload.Version}",
            $"Objective: {procedure.Payload.Objective}",
            $"Scope: {procedure.Payload.Scope}",
            $"Policy: {procedure.Payload.Policy}",
            $"Responsibility: {procedure.Payload.Responsibility}",
            $"Definitions: {procedure.Payload.Definitions}",
            $"Description: {procedure.Payload.Description}"
        };

        // Add flow steps
        if (procedure.Payload.FlowSteps.Any())
        {
            content.Add("Flow Steps:");
            foreach (var step in procedure.Payload.FlowSteps)
            {
                content.Add($"Step {step.Step}: {step.Action} (Responsible: {step.Responsible}, Duration: {step.Duration})");
            }
        }

        // Add RACI matrix
        if (procedure.Payload.Raci.Any())
        {
            content.Add("RACI Matrix:");
            foreach (var raci in procedure.Payload.Raci)
            {
                content.Add($"Task: {raci.Task} (Responsible: {raci.Responsible}, Accountable: {raci.Accountable})");
            }
        }

        // Add KPIs
        if (procedure.Payload.Kpis.Any())
        {
            content.Add("KPIs:");
            foreach (var kpi in procedure.Payload.Kpis)
            {
                content.Add($"KPI: {kpi.Name} ({kpi.Type}) - Target: {kpi.Value}");
            }
        }

        // Add forms
        if (procedure.Payload.Forms.Any())
        {
            content.Add("Related Forms:");
            foreach (var form in procedure.Payload.Forms)
            {
                content.Add($"Form: {form.Name} (Code: {form.Code}, Retention: {form.Retention})");
            }
        }

        // Add references
        if (procedure.Payload.References.Any())
        {
            content.Add("References:");
            content.AddRange(procedure.Payload.References);
        }

        return string.Join("\n", content.Where(c => !string.IsNullOrWhiteSpace(c)));
    }
}
