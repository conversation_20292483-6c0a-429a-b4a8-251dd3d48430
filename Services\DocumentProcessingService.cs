using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ChatbotApp.Models;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ChatbotApp.Services;

public class DocumentProcessingService : IDocumentProcessingService
{
    private readonly ILogger<DocumentProcessingService> _logger;
    private readonly IVectorService _vectorService;
    private static readonly List<DocumentInfo> _documents = new(); // Static for shared storage

    public DocumentProcessingService(ILogger<DocumentProcessingService> logger, IVectorService vectorService)
    {
        _logger = logger;
        _vectorService = vectorService;
    }

    public async Task<DocumentProcessingResult> ProcessDocumentAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return new DocumentProcessingResult
                {
                    Success = false,
                    Message = "File not found",
                    Errors = new List<string> { $"File not found: {filePath}" }
                };
            }

            using var fileStream = File.OpenRead(filePath);
            var fileName = Path.GetFileName(filePath);
            return await ProcessDocumentAsync(fileStream, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document from file path: {FilePath}", filePath);
            return new DocumentProcessingResult
            {
                Success = false,
                Message = "Error processing document",
                Errors = new List<string> { ex.Message }
            };
        }
    }

    public async Task<DocumentProcessingResult> ProcessDocumentAsync(Stream fileStream, string fileName)
    {
        try
        {
            var documentInfo = await ExtractDocumentInfoAsync(fileStream, fileName);
            
            // Store document info
            _documents.Add(documentInfo);
            
            // Create vector embeddings for the document
            await _vectorService.IndexDocumentAsync(documentInfo);

            _logger.LogInformation("Successfully processed document: {FileName}", fileName);

            return new DocumentProcessingResult
            {
                Success = true,
                Message = "Document processed successfully",
                DocumentInfo = documentInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document: {FileName}", fileName);
            return new DocumentProcessingResult
            {
                Success = false,
                Message = "Error processing document",
                Errors = new List<string> { ex.Message }
            };
        }
    }

    private async Task<DocumentInfo> ExtractDocumentInfoAsync(Stream fileStream, string fileName)
    {
        var documentInfo = new DocumentInfo
        {
            FileName = fileName,
            FilePath = $"wwwroot/files/{fileName}",
            FileSize = fileStream.Length
        };

        if (Path.GetExtension(fileName).ToLower() == ".docx")
        {
            await ExtractWordDocumentInfoAsync(fileStream, documentInfo);
        }
        else
        {
            // For other file types, just read as text
            using var reader = new StreamReader(fileStream);
            documentInfo.Content = await reader.ReadToEndAsync();
            documentInfo.Title = Path.GetFileNameWithoutExtension(fileName);
        }

        // Generate summary and keywords
        documentInfo.Summary = GenerateSummary(documentInfo.Content);
        documentInfo.Keywords = ExtractKeywords(documentInfo.Content);

        // Create metadata JSON
        documentInfo.Metadata = new Dictionary<string, object>
        {
            ["fileType"] = Path.GetExtension(fileName),
            ["wordCount"] = CountWords(documentInfo.Content),
            ["characterCount"] = documentInfo.Content.Length,
            ["processedDate"] = DateTime.UtcNow,
            ["hasTitle"] = !string.IsNullOrEmpty(documentInfo.Title),
            ["keywordCount"] = documentInfo.Keywords.Count
        };

        return documentInfo;
    }

    private async Task ExtractWordDocumentInfoAsync(Stream fileStream, DocumentInfo documentInfo)
    {
        using var wordDocument = WordprocessingDocument.Open(fileStream, false);
        var body = wordDocument.MainDocumentPart?.Document?.Body;
        
        if (body == null)
        {
            _logger.LogWarning("Could not extract body from Word document: {FileName}", documentInfo.FileName);
            return;
        }

        var contentBuilder = new StringBuilder();
        var titleExtracted = false;

        // Extract text content
        foreach (var element in body.Elements())
        {
            if (element is Paragraph paragraph)
            {
                var text = GetTextFromParagraph(paragraph);
                if (!string.IsNullOrWhiteSpace(text))
                {
                    // Use first non-empty paragraph as title if not set
                    if (!titleExtracted && string.IsNullOrEmpty(documentInfo.Title))
                    {
                        documentInfo.Title = text.Trim();
                        titleExtracted = true;
                    }
                    
                    contentBuilder.AppendLine(text);
                }
            }
            else if (element is Table table)
            {
                var tableText = ExtractTableText(table);
                if (!string.IsNullOrWhiteSpace(tableText))
                {
                    contentBuilder.AppendLine(tableText);
                }
            }
        }

        documentInfo.Content = contentBuilder.ToString().Trim();

        // Extract document properties
        var coreProperties = wordDocument.PackageProperties;
        if (coreProperties != null)
        {
            documentInfo.Author = coreProperties.Creator ?? string.Empty;
            documentInfo.CreatedDate = coreProperties.Created ?? DateTime.UtcNow;
            documentInfo.ModifiedDate = coreProperties.Modified ?? DateTime.UtcNow;
            
            if (!string.IsNullOrEmpty(coreProperties.Title))
            {
                documentInfo.Title = coreProperties.Title;
            }
        }

        await Task.CompletedTask;
    }

    private string GetTextFromParagraph(Paragraph paragraph)
    {
        var textBuilder = new StringBuilder();
        foreach (var run in paragraph.Elements<Run>())
        {
            foreach (var text in run.Elements<Text>())
            {
                textBuilder.Append(text.Text);
            }
        }
        return textBuilder.ToString();
    }

    private string ExtractTableText(Table table)
    {
        var tableBuilder = new StringBuilder();
        foreach (var row in table.Elements<TableRow>())
        {
            var rowText = new List<string>();
            foreach (var cell in row.Elements<TableCell>())
            {
                var cellText = new StringBuilder();
                foreach (var paragraph in cell.Elements<Paragraph>())
                {
                    cellText.Append(GetTextFromParagraph(paragraph) + " ");
                }
                rowText.Add(cellText.ToString().Trim());
            }
            tableBuilder.AppendLine(string.Join(" | ", rowText));
        }
        return tableBuilder.ToString();
    }

    private string GenerateSummary(string content, int maxLength = 200)
    {
        if (string.IsNullOrWhiteSpace(content))
            return string.Empty;

        // Simple summary: take first few sentences
        var sentences = content.Split('.', StringSplitOptions.RemoveEmptyEntries);
        var summary = new StringBuilder();
        
        foreach (var sentence in sentences.Take(3))
        {
            var cleanSentence = sentence.Trim();
            if (!string.IsNullOrEmpty(cleanSentence))
            {
                summary.Append(cleanSentence + ". ");
                if (summary.Length > maxLength)
                    break;
            }
        }

        var result = summary.ToString().Trim();
        return result.Length > maxLength ? result.Substring(0, maxLength) + "..." : result;
    }

    private List<string> ExtractKeywords(string content, int maxKeywords = 10)
    {
        if (string.IsNullOrWhiteSpace(content))
            return new List<string>();

        // Simple keyword extraction: find most common words (excluding common stop words)
        var stopWords = new HashSet<string> { "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "this", "that", "these", "those" };
        
        var words = Regex.Matches(content.ToLower(), @"\b\w{3,}\b")
            .Cast<Match>()
            .Select(m => m.Value)
            .Where(word => !stopWords.Contains(word))
            .GroupBy(word => word)
            .OrderByDescending(g => g.Count())
            .Take(maxKeywords)
            .Select(g => g.Key)
            .ToList();

        return words;
    }

    private int CountWords(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return 0;

        return Regex.Matches(content, @"\b\w+\b").Count;
    }

    public async Task<List<DocumentInfo>> GetAllDocumentsAsync()
    {
        return await Task.FromResult(_documents.ToList());
    }

    public async Task<DocumentInfo?> GetDocumentByIdAsync(string documentId)
    {
        return await Task.FromResult(_documents.FirstOrDefault(d => d.Id == documentId));
    }

    public async Task<bool> DeleteDocumentAsync(string documentId)
    {
        var document = _documents.FirstOrDefault(d => d.Id == documentId);
        if (document != null)
        {
            _documents.Remove(document);
            await _vectorService.DeleteDocumentAsync(documentId);
            return true;
        }
        return false;
    }
}
