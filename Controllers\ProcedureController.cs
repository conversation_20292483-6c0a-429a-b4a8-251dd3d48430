using ChatbotApp.Models;
using ChatbotApp.Services;
using ChatbotApp.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.SemanticKernel;
using System.Text.Json;


namespace ChatbotApp.Controllers;

public class ProcedureController : Controller
{
    private readonly ILogger<ProcedureController> _logger;
    private readonly string _dataPath;
    private readonly IProcedureVectorService _procedureVectorService;

    public ProcedureController(ILogger<ProcedureController> logger, IWebHostEnvironment environment, IProcedureVectorService procedureVectorService)
    {
        _logger = logger;
        _dataPath = Path.Combine(environment.WebRootPath, "data");
        _procedureVectorService = procedureVectorService;
        
        // Ensure data directory exists
        if (!Directory.Exists(_dataPath))
        {
            Directory.CreateDirectory(_dataPath);
        }
    }

    // GET: Procedure
    public async Task<IActionResult> Index()
    {
        var xx = await _procedureVectorService.IsAvailableAsync();
        var procedureVectorItems = GetAllProcedures();
        // Convert to Procedure for the view
        var procedures = procedureVectorItems.Select(p => new Procedure
        {
            Id = p.Id,
            Sector = p.Sector,
            Department = p.Department,
            Section = p.Section,
            Title = p.Title,
            Version = p.Version,
            Objective = p.Objective,
            Scope = p.Scope,
            Policy = p.Policy,
            Responsibility = p.Responsibility,
            Definitions = p.Definitions,
            Description = p.Description,
            FlowSteps = p.FlowSteps,
            Raci = p.Raci,
            Kpis = p.Kpis,
            Forms = p.Forms,
            References = p.References
        }).ToList();

        return View(procedures);
    }

    // GET: Procedure/Create
    public IActionResult Create()
    {
        var viewModel = new ProcedureViewModel();
        return View(viewModel);
    }

    // POST: Procedure/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ProcedureViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                // Parse JSON strings back to objects
                if (!string.IsNullOrEmpty(viewModel.FlowStepsJson))
                {
                    viewModel.Procedure.FlowSteps = JsonSerializer.Deserialize<List<ProcedureStep>>(viewModel.FlowStepsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.RaciJson))
                {
                    viewModel.Procedure.Raci = JsonSerializer.Deserialize<List<ProcedureRaci>>(viewModel.RaciJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.KpisJson))
                {
                    viewModel.Procedure.Kpis = JsonSerializer.Deserialize<List<ProcedureKpi>>(viewModel.KpisJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.FormsJson))
                {
                    viewModel.Procedure.Forms = JsonSerializer.Deserialize<List<ProcedureForm>>(viewModel.FormsJson) ?? new();
                }

                // Parse references from comma-separated text
                if (!string.IsNullOrEmpty(viewModel.ReferencesText))
                {
                    viewModel.Procedure.References = viewModel.ReferencesText
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .ToList();
                }

                // Save to JSON file
                var fileName = $"{viewModel.Procedure.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(viewModel.Procedure, options);
                await System.IO.File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("Procedure {Id} saved successfully to {FilePath}", viewModel.Procedure.Id, filePath);
                
                TempData["SuccessMessage"] = $"Procedure {viewModel.Procedure.Id} has been saved successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving procedure {Id}", viewModel.Procedure?.Id);
            ModelState.AddModelError("", $"Error saving procedure: {ex.Message}");
        }

        return View(viewModel);
    }

    // GET: Procedure/Edit/5
    public IActionResult Edit(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedure = GetProcedureById(id);
        if (procedure == null)
        {
            return NotFound();
        }

        // Convert ProcedureVectorItem to Procedure for the view
        var procedureForView = new Procedure
        {
            Id = procedure.Id,
            Sector = procedure.Payload.Sector,
            Department = procedure.Payload.Department,
            Section = procedure.Payload.Section,
            Title = procedure.Payload.Title,
            Version = procedure.Payload.Version,
            Objective = procedure.Payload.Objective,
            Scope = procedure.Payload.Scope,
            Policy = procedure.Payload.Policy,
            Responsibility = procedure.Payload.Responsibility,
            Definitions = procedure.Payload.Definitions,
            Description = procedure.Payload.Description,
            FlowSteps = procedure.Payload.FlowSteps,
            Raci = procedure.Payload.Raci,
            Kpis = procedure.Payload.Kpis,
            Forms = procedure.Payload.Forms,
            References = procedure.Payload.References
        };

        var viewModel = new ProcedureViewModel
        {
            Procedure = procedureForView,
            FlowStepsJson = JsonSerializer.Serialize(procedure.Payload.FlowSteps),
            RaciJson = JsonSerializer.Serialize(procedure.Payload.Raci),
            KpisJson = JsonSerializer.Serialize(procedure.Payload.Kpis),
            FormsJson = JsonSerializer.Serialize(procedure.Payload.Forms),
            ReferencesText = string.Join(", ", procedure.Payload.References)
        };

        return View(viewModel);
    }

    // POST: Procedure/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string id, ProcedureViewModel viewModel)
    {
        if (id != viewModel.Procedure.Id)
        {
            return NotFound();
        }

        try
        {
            if (ModelState.IsValid)
            {
                // Parse JSON strings back to objects (same logic as Create)
                if (!string.IsNullOrEmpty(viewModel.FlowStepsJson))
                {
                    viewModel.Procedure.FlowSteps = JsonSerializer.Deserialize<List<ProcedureStep>>(viewModel.FlowStepsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.RaciJson))
                {
                    viewModel.Procedure.Raci = JsonSerializer.Deserialize<List<ProcedureRaci>>(viewModel.RaciJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.KpisJson))
                {
                    viewModel.Procedure.Kpis = JsonSerializer.Deserialize<List<ProcedureKpi>>(viewModel.KpisJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.FormsJson))
                {
                    viewModel.Procedure.Forms = JsonSerializer.Deserialize<List<ProcedureForm>>(viewModel.FormsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.ReferencesText))
                {
                    viewModel.Procedure.References = viewModel.ReferencesText
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .ToList();
                }

                // Save to JSON file
                var fileName = $"{viewModel.Procedure.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(viewModel.Procedure, options);
                await System.IO.File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("Procedure {Id} updated successfully", viewModel.Procedure.Id);
                
                TempData["SuccessMessage"] = $"Procedure {viewModel.Procedure.Id} has been updated successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating procedure {Id}", viewModel.Procedure?.Id);
            ModelState.AddModelError("", $"Error updating procedure: {ex.Message}");
        }

        return View(viewModel);
    }

    // GET: Procedure/Details/5
    public IActionResult Details(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedureVectorItem = GetProcedureById(id);
        if (procedureVectorItem == null)
        {
            return NotFound();
        }

        // Convert ProcedureVectorItem to Procedure for the view
        var procedure = new Procedure
        {
            Id = procedureVectorItem.Id,
            Sector = procedureVectorItem.Payload.Sector,
            Department = procedureVectorItem.Payload.Department,
            Section = procedureVectorItem.Payload.Section,
            Title = procedureVectorItem.Payload.Title,
            Version = procedureVectorItem.Payload.Version,
            Objective = procedureVectorItem.Payload.Objective,
            Scope = procedureVectorItem.Payload.Scope,
            Policy = procedureVectorItem.Payload.Policy,
            Responsibility = procedureVectorItem.Payload.Responsibility,
            Definitions = procedureVectorItem.Payload.Definitions,
            Description = procedureVectorItem.Payload.Description,
            FlowSteps = procedureVectorItem.Payload.FlowSteps,
            Raci = procedureVectorItem.Payload.Raci,
            Kpis = procedureVectorItem.Payload.Kpis,
            Forms = procedureVectorItem.Payload.Forms,
            References = procedureVectorItem.Payload.References
        };

        return View(procedure);
    }

    // GET: Procedure/Delete/5
    public IActionResult Delete(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedure = GetProcedureById(id);
        if (procedure == null)
        {
            return NotFound();
        }

        return View(procedure);
    }

    // POST: Procedure/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public IActionResult DeleteConfirmed(string id)
    {
        try
        {
            var fileName = $"{id}.json";
            var filePath = Path.Combine(_dataPath, fileName);
            
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
                _logger.LogInformation("Procedure {Id} deleted successfully", id);
                TempData["SuccessMessage"] = $"Procedure {id} has been deleted successfully!";
            }
            else
            {
                TempData["ErrorMessage"] = $"Procedure {id} not found!";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting procedure {Id}", id);
            TempData["ErrorMessage"] = $"Error deleting procedure: {ex.Message}";
        }

        return RedirectToAction(nameof(Index));
    }

    // Helper methods
    private List<Procedure> GetAllProcedures()
    {
        var procedures = new List<Procedure>();

        try
        {
            if (Directory.Exists(_dataPath))
            {
                var jsonFiles = Directory.GetFiles(_dataPath, "*.json");

                foreach (var file in jsonFiles)
                {
                    try
                    {
                        var json = System.IO.File.ReadAllText(file);
                        var procedure = JsonSerializer.Deserialize<Procedure>(json);
                        if (procedure != null)
                        {
                            procedures.Add(procedure);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error reading procedure file {File}", file);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading procedures");
        }

        return procedures.OrderBy(p => p.Id).ToList();
    }

    private ProcedureVectorItem? GetProcedureById(string id)
    {
        try
        {
            var fileName = $"{id}.json";
            var filePath = Path.Combine(_dataPath, fileName);

            if (System.IO.File.Exists(filePath))
            {
                var json = System.IO.File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<ProcedureVectorItem>(json);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading procedure {Id}", id);
        }

        return null;
    }

    // Vector upload actions
    [HttpPost]
    public async Task<IActionResult> UploadToVector(string id)
    {
        try
        {
            var procedure = GetProcedureById(id);
            if (procedure == null)
            {
                return Json(new { success = false, message = "Procedure not found" });
            }

            // Upload to vector database using ProcedureVectorService
            var success = await _procedureVectorService.IndexProcedureAsync(procedure);

            if (success)
            {
                _logger.LogInformation("Successfully uploaded procedure {Id} to vector database", id);
                return Json(new { success = true, message = "Procedure uploaded to vector database successfully" });
            }
            else
            {
                return Json(new { success = false, message = "Failed to upload procedure to vector database" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading procedure {Id} to vector database", id);
            return Json(new { success = false, message = $"Error: {ex.Message}" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UploadAllToVector()
    {
        try
        {
            var procedures = GetAllProcedures();
            var xx = procedures.Select(p=>p.ToVectorItem()).ToList();
            // Upload all procedures to vector database
            var success = await _procedureVectorService.IndexAllProceduresAsync();

            if (success)
            {
                _logger.LogInformation("Successfully uploaded {Count} procedures to vector database", procedures.Count);
                return Json(new { success = true, message = $"All {procedures.Count} procedures uploaded to vector database successfully" });
            }
            else
            {
                return Json(new { success = false, message = "Failed to upload procedures to vector database" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading all procedures to vector database");
            return Json(new { success = false, message = $"Error: {ex.Message}" });
        }
    }

    [HttpGet]
    public async Task<IActionResult> VectorStatus()
    {
        try
        {
            // Check actual vector service status
            var isAvailable = await _procedureVectorService.IsAvailableAsync();
            return Json(new {
                success = true,
                vectorServiceAvailable = isAvailable,
                message = isAvailable ? "Vector service is available" : "Vector service is not available"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking vector service status");
            return Json(new { success = false, message = $"Error: {ex.Message}" });
        }
    }
}
