using Microsoft.AspNetCore.Mvc;
using ChatbotApp.Models;
using System.Text.Json;

namespace ChatbotApp.Controllers;

public class ProcedureController : Controller
{
    private readonly ILogger<ProcedureController> _logger;
    private readonly string _dataPath;

    public ProcedureController(ILogger<ProcedureController> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _dataPath = Path.Combine(environment.WebRootPath, "data");
        
        // Ensure data directory exists
        if (!Directory.Exists(_dataPath))
        {
            Directory.CreateDirectory(_dataPath);
        }
    }

    // GET: Procedure
    public IActionResult Index()
    {
        var procedures = GetAllProcedures();
        return View(procedures);
    }

    // GET: Procedure/Create
    public IActionResult Create()
    {
        var viewModel = new ProcedureViewModel();
        return View(viewModel);
    }

    // POST: Procedure/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ProcedureViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                // Parse JSON strings back to objects
                if (!string.IsNullOrEmpty(viewModel.FlowStepsJson))
                {
                    viewModel.Procedure.Payload.FlowSteps = JsonSerializer.Deserialize<List<ProcedureStep>>(viewModel.FlowStepsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.RaciJson))
                {
                    viewModel.Procedure.Payload.Raci = JsonSerializer.Deserialize<List<ProcedureRaci>>(viewModel.RaciJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.KpisJson))
                {
                    viewModel.Procedure.Payload.Kpis = JsonSerializer.Deserialize<List<ProcedureKpi>>(viewModel.KpisJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.FormsJson))
                {
                    viewModel.Procedure.Payload.Forms = JsonSerializer.Deserialize<List<ProcedureForm>>(viewModel.FormsJson) ?? new();
                }

                // Parse references from comma-separated text
                if (!string.IsNullOrEmpty(viewModel.ReferencesText))
                {
                    viewModel.Procedure.Payload.References = viewModel.ReferencesText
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .ToList();
                }

                // Save to JSON file
                var fileName = $"{viewModel.Procedure.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(viewModel.Procedure, options);
                await System.IO.File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("Procedure {Id} saved successfully to {FilePath}", viewModel.Procedure.Id, filePath);
                
                TempData["SuccessMessage"] = $"Procedure {viewModel.Procedure.Id} has been saved successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving procedure {Id}", viewModel.Procedure?.Id);
            ModelState.AddModelError("", $"Error saving procedure: {ex.Message}");
        }

        return View(viewModel);
    }

    // GET: Procedure/Edit/5
    public IActionResult Edit(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedure = GetProcedureById(id);
        if (procedure == null)
        {
            return NotFound();
        }

        var viewModel = new ProcedureViewModel
        {
            Procedure = procedure,
            FlowStepsJson = JsonSerializer.Serialize(procedure.Payload.FlowSteps),
            RaciJson = JsonSerializer.Serialize(procedure.Payload.Raci),
            KpisJson = JsonSerializer.Serialize(procedure.Payload.Kpis),
            FormsJson = JsonSerializer.Serialize(procedure.Payload.Forms),
            ReferencesText = string.Join(", ", procedure.Payload.References)
        };

        return View(viewModel);
    }

    // POST: Procedure/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string id, ProcedureViewModel viewModel)
    {
        if (id != viewModel.Procedure.Id)
        {
            return NotFound();
        }

        try
        {
            if (ModelState.IsValid)
            {
                // Parse JSON strings back to objects (same logic as Create)
                if (!string.IsNullOrEmpty(viewModel.FlowStepsJson))
                {
                    viewModel.Procedure.Payload.FlowSteps = JsonSerializer.Deserialize<List<ProcedureStep>>(viewModel.FlowStepsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.RaciJson))
                {
                    viewModel.Procedure.Payload.Raci = JsonSerializer.Deserialize<List<ProcedureRaci>>(viewModel.RaciJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.KpisJson))
                {
                    viewModel.Procedure.Payload.Kpis = JsonSerializer.Deserialize<List<ProcedureKpi>>(viewModel.KpisJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.FormsJson))
                {
                    viewModel.Procedure.Payload.Forms = JsonSerializer.Deserialize<List<ProcedureForm>>(viewModel.FormsJson) ?? new();
                }

                if (!string.IsNullOrEmpty(viewModel.ReferencesText))
                {
                    viewModel.Procedure.Payload.References = viewModel.ReferencesText
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .ToList();
                }

                // Save to JSON file
                var fileName = $"{viewModel.Procedure.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(viewModel.Procedure, options);
                await System.IO.File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("Procedure {Id} updated successfully", viewModel.Procedure.Id);
                
                TempData["SuccessMessage"] = $"Procedure {viewModel.Procedure.Id} has been updated successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating procedure {Id}", viewModel.Procedure?.Id);
            ModelState.AddModelError("", $"Error updating procedure: {ex.Message}");
        }

        return View(viewModel);
    }

    // GET: Procedure/Details/5
    public IActionResult Details(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedure = GetProcedureById(id);
        if (procedure == null)
        {
            return NotFound();
        }

        return View(procedure);
    }

    // GET: Procedure/Delete/5
    public IActionResult Delete(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        var procedure = GetProcedureById(id);
        if (procedure == null)
        {
            return NotFound();
        }

        return View(procedure);
    }

    // POST: Procedure/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public IActionResult DeleteConfirmed(string id)
    {
        try
        {
            var fileName = $"{id}.json";
            var filePath = Path.Combine(_dataPath, fileName);
            
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
                _logger.LogInformation("Procedure {Id} deleted successfully", id);
                TempData["SuccessMessage"] = $"Procedure {id} has been deleted successfully!";
            }
            else
            {
                TempData["ErrorMessage"] = $"Procedure {id} not found!";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting procedure {Id}", id);
            TempData["ErrorMessage"] = $"Error deleting procedure: {ex.Message}";
        }

        return RedirectToAction(nameof(Index));
    }

    // Helper methods
    private List<ProcedureVectorItem> GetAllProcedures()
    {
        var procedures = new List<ProcedureVectorItem>();
        
        try
        {
            if (Directory.Exists(_dataPath))
            {
                var jsonFiles = Directory.GetFiles(_dataPath, "*.json");
                
                foreach (var file in jsonFiles)
                {
                    try
                    {
                        var json = System.IO.File.ReadAllText(file);
                        var procedure = JsonSerializer.Deserialize<ProcedureVectorItem>(json);
                        if (procedure != null)
                        {
                            procedures.Add(procedure);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error reading procedure file {File}", file);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading procedures");
        }

        return procedures.OrderBy(p => p.Id).ToList();
    }

    private ProcedureVectorItem? GetProcedureById(string id)
    {
        try
        {
            var fileName = $"{id}.json";
            var filePath = Path.Combine(_dataPath, fileName);
            
            if (System.IO.File.Exists(filePath))
            {
                var json = System.IO.File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<ProcedureVectorItem>(json);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading procedure {Id}", id);
        }

        return null;
    }
}
