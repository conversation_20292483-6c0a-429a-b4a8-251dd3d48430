using Microsoft.AspNetCore.Mvc;
using ChatbotApp.Services;

namespace ChatbotApp.Controllers;

public class ChatController : Controller
{
    private readonly IOllamaService _ollamaService;
    private readonly ILogger<ChatController> _logger;

    public ChatController(IOllamaService ollamaService, ILogger<ChatController> logger)
    {
        _ollamaService = ollamaService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        ViewBag.OllamaAvailable = await _ollamaService.IsAvailableAsync();
        return View();
    }
}
