{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "js/chat.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}, {"Name": "label", "Value": "js/chat.js"}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "js/chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}, {"Name": "label", "Value": "js/chat.js"}]}, {"Route": "js/chat.5g4imy9nsy.js.gz", "AssetFile": "js/chat.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}, {"Name": "label", "Value": "js/chat.js.gz"}]}, {"Route": "js/chat.js", "AssetFile": "js/chat.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js", "AssetFile": "js/chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js.gz", "AssetFile": "js/chat.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}]}