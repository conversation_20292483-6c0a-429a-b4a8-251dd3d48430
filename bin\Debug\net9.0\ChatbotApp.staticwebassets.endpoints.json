{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "data/FIN-03.json", "AssetFile": "data/FIN-03.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000877963126"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "ETag", "Value": "W/\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.json", "AssetFile": "data/FIN-03.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3709"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.json.gz", "AssetFile": "data/FIN-03.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw="}]}, {"Route": "data/FIN-03.wi72cewi0b.json", "AssetFile": "data/FIN-03.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000877963126"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "ETag", "Value": "W/\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}, {"Name": "label", "Value": "data/FIN-03.json"}]}, {"Route": "data/FIN-03.wi72cewi0b.json", "AssetFile": "data/FIN-03.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3709"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}, {"Name": "label", "Value": "data/FIN-03.json"}]}, {"Route": "data/FIN-03.wi72cewi0b.json.gz", "AssetFile": "data/FIN-03.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "integrity", "Value": "sha256-yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw="}, {"Name": "label", "Value": "data/FIN-03.json.gz"}]}, {"Route": "data/HR-07.if7imjr4iq.json", "AssetFile": "data/HR-07.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000781250000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "ETag", "Value": "W/\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}, {"Name": "label", "Value": "data/HR-07.json"}]}, {"Route": "data/HR-07.if7imjr4iq.json", "AssetFile": "data/HR-07.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}, {"Name": "label", "Value": "data/HR-07.json"}]}, {"Route": "data/HR-07.if7imjr4iq.json.gz", "AssetFile": "data/HR-07.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "integrity", "Value": "sha256-DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8="}, {"Name": "label", "Value": "data/HR-07.json.gz"}]}, {"Route": "data/HR-07.json", "AssetFile": "data/HR-07.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000781250000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "ETag", "Value": "W/\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.json", "AssetFile": "data/HR-07.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.json.gz", "AssetFile": "data/HR-07.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8="}]}, {"Route": "files/PR_1.14askf0i1a.docx", "AssetFile": "files/PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "14askf0i1a"}, {"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}, {"Name": "label", "Value": "files/PR_1.docx"}]}, {"Route": "files/PR_1.docx", "AssetFile": "files/PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "js/chat.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}, {"Name": "label", "Value": "js/chat.js"}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "js/chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}, {"Name": "label", "Value": "js/chat.js"}]}, {"Route": "js/chat.5g4imy9nsy.js.gz", "AssetFile": "js/chat.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}, {"Name": "label", "Value": "js/chat.js.gz"}]}, {"Route": "js/chat.js", "AssetFile": "js/chat.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js", "AssetFile": "js/chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js.gz", "AssetFile": "js/chat.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "js/documents.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}, {"Name": "label", "Value": "js/documents.js"}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "js/documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}, {"Name": "label", "Value": "js/documents.js"}]}, {"Route": "js/documents.awmycvggfz.js.gz", "AssetFile": "js/documents.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}, {"Name": "label", "Value": "js/documents.js.gz"}]}, {"Route": "js/documents.js", "AssetFile": "js/documents.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js", "AssetFile": "js/documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js.gz", "AssetFile": "js/documents.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}]}]}