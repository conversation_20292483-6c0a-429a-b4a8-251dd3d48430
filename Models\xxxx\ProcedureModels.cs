using Microsoft.Extensions.VectorData;

namespace ChatbotApp.Models.xxxx;
    /*

public class BaseVectorClass
{
    [VectorStoreKey]
    public Guid Key { get; set; } = Guid.NewGuid();

    [VectorStoreData]
    public string Description { get; set; } = null;


    [VectorStoreVector(768, DistanceFunction = DistanceFunction.CosineSimilarity)]
    public ReadOnlyMemory<float>? DescriptionEmbedding { get; set; }// بيانات الإجراء


}

public class ProcedureVectorItem 
{

    [VectorStoreData]

    public string Id { get; set; } = string.Empty;                     // مثال: "HR-07"
    [VectorStoreData]

    public string Title { get; set; } = string.Empty;
    [VectorStoreData]

    public float[] Vector { get; set; } = Array.Empty<float>();        // سيتم ملؤه لاحقاً من نموذج embeddings
    [VectorStoreData]

    public string Sector { get; set; } = string.Empty;
    [VectorStoreData]

    public string Department { get; set; } = string.Empty;
    [VectorStoreData]

    public string Section { get; set; } = string.Empty;
    [VectorStoreData]

    public string Version { get; set; } = string.Empty;
    [VectorStoreData]

    public string Objective { get; set; } = string.Empty;
    [VectorStoreData]

    public string Scope { get; set; } = string.Empty;
    [VectorStoreData]

    public string Policy { get; set; } = string.Empty;
    [VectorStoreData]

    public string Responsibility { get; set; } = string.Empty;
    [VectorStoreData]

    public string Definitions { get; set; } = string.Empty;
    [VectorStoreData]

    public string Description { get; set; } = string.Empty;
    public ProcedurePayload Payload { get; set; } = null;

    private string ConvertDeserializedPayloadToJson() => Description = System.Text.Json.JsonSerializer.Serialize(Payload);

}

public class ProcedurePayload
{


    public List<ProcedureStep> FlowSteps { get; set; } = new();
    public List<ProcedureRaci> Raci { get; set; } = new();
    public List<ProcedureKpi> Kpis { get; set; } = new();
    public List<ProcedureForm> Forms { get; set; } = new();
    public List<string> References { get; set; } = new();
}

public class ProcedureStep
{
    public int Step { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Responsible { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
}

public class ProcedureRaci
{
    public string Task { get; set; } = string.Empty;
    public string Responsible { get; set; } = string.Empty;
    public string Accountable { get; set; } = string.Empty;
}

public class ProcedureKpi
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class ProcedureForm
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Retention { get; set; } = string.Empty;
}
*/