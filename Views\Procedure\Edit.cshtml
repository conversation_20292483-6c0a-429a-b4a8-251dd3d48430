@model ChatbotApp.ViewModels.ProcedureViewModel

@{
    ViewData["Title"] = "Edit Procedure";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-edit me-2"></i>
                    Edit Procedure: @Model.Procedure.Id
                </h2>
                <div>
                    <a asp-action="Details" asp-route-id="@Model.Procedure.Id" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>
                        View Details
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to List
                    </a>
                </div>
            </div>

            <form asp-action="Edit" method="post">
                <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Id" class="form-label">Procedure ID *</label>
                                    <input asp-for="Procedure.Id" class="form-control" readonly />
                                    <div class="form-text">ID cannot be changed after creation</div>
                                    <span asp-validation-for="Procedure.Id" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Version" class="form-label">Version *</label>
                                    <input asp-for="Procedure.Version" class="form-control" required />
                                    <span asp-validation-for="Procedure.Version" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Sector" class="form-label">Sector *</label>
                                    <input asp-for="Procedure.Sector" class="form-control" required />
                                    <span asp-validation-for="Procedure.Sector" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Department" class="form-label">Department *</label>
                                    <input asp-for="Procedure.Department" class="form-control" required />
                                    <span asp-validation-for="Procedure.Department" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Section" class="form-label">Section</label>
                                    <input asp-for="Procedure.Section" class="form-control" />
                                    <span asp-validation-for="Procedure.Section" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Title" class="form-label">Title *</label>
                            <input asp-for="Procedure.Title" class="form-control" required />
                            <span asp-validation-for="Procedure.Title" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Procedure Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Procedure.Objective" class="form-label">Objective</label>
                            <textarea asp-for="Procedure.Objective" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Objective" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Scope" class="form-label">Scope</label>
                            <textarea asp-for="Procedure.Scope" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Scope" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Policy" class="form-label">Policy</label>
                            <textarea asp-for="Procedure.Policy" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Policy" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Responsibility" class="form-label">Responsibility</label>
                            <textarea asp-for="Procedure.Responsibility" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Responsibility" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Definitions" class="form-label">Definitions</label>
                            <textarea asp-for="Procedure.Definitions" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Definitions" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Description" class="form-label">Description</label>
                            <textarea asp-for="Procedure.Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Procedure.Description" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ReferencesText" class="form-label">References</label>
                            <textarea asp-for="ReferencesText" class="form-control" rows="2" 
                                      placeholder="Enter references separated by commas"></textarea>
                            <div class="form-text">Enter multiple references separated by commas</div>
                            <span asp-validation-for="ReferencesText" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Flow Steps Section -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list-ol me-2"></i>
                            Flow Steps
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="flowStepsContainer">
                            <!-- Flow steps will be loaded here -->
                        </div>
                        <button type="button" class="btn btn-outline-success" onclick="addFlowStep()">
                            <i class="fas fa-plus me-2"></i>Add Flow Step
                        </button>
                    </div>
                </div>

                <!-- RACI Matrix Section -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            RACI Matrix
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="raciContainer">
                            <!-- RACI items will be loaded here -->
                        </div>
                        <button type="button" class="btn btn-outline-warning" onclick="addRaciItem()">
                            <i class="fas fa-plus me-2"></i>Add RACI Item
                        </button>
                    </div>
                </div>

                <!-- KPIs Section -->
                <div class="card mt-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Key Performance Indicators (KPIs)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="kpisContainer">
                            <!-- KPIs will be loaded here -->
                        </div>
                        <button type="button" class="btn btn-outline-danger" onclick="addKpi()">
                            <i class="fas fa-plus me-2"></i>Add KPI
                        </button>
                    </div>
                </div>

                <!-- Forms Section -->
                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-invoice me-2"></i>
                            Related Forms
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="formsContainer">
                            <!-- Forms will be loaded here -->
                        </div>
                        <button type="button" class="btn btn-outline-secondary" onclick="addForm()">
                            <i class="fas fa-plus me-2"></i>Add Form
                        </button>
                    </div>
                </div>

                <!-- Hidden fields for JSON data -->
                <input type="hidden" asp-for="FlowStepsJson" id="flowStepsJson" />
                <input type="hidden" asp-for="RaciJson" id="raciJson" />
                <input type="hidden" asp-for="KpisJson" id="kpisJson" />
                <input type="hidden" asp-for="FormsJson" id="formsJson" />

                <div class="mt-4 mb-4">
                    <button type="submit" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-save me-2"></i>
                        Update Procedure
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.Procedure.Id" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-eye me-2"></i>
                        View Details
                    </a>
                    <a asp-action="Index" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        let flowStepCounter = 0;
        let raciCounter = 0;
        let kpiCounter = 0;
        let formCounter = 0;

        // Flow Steps Functions
        function addFlowStep(stepData = null) {
            flowStepCounter++;
            const container = document.getElementById('flowStepsContainer');
            const stepDiv = document.createElement('div');
            stepDiv.className = 'card mb-3';
            stepDiv.id = `flowStep_${flowStepCounter}`;
            stepDiv.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Flow Step ${flowStepCounter}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFlowStep(${flowStepCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <label class="form-label">Step Number</label>
                            <input type="number" class="form-control flow-step-number" value="${stepData ? stepData.Step : flowStepCounter}" min="1" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Action</label>
                            <input type="text" class="form-control flow-step-action" placeholder="Action description" value="${stepData ? stepData.Action : ''}" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Responsible</label>
                            <input type="text" class="form-control flow-step-responsible" placeholder="Role/Person" value="${stepData ? stepData.Responsible : ''}" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Duration</label>
                            <input type="text" class="form-control flow-step-duration" placeholder="Time required" value="${stepData ? stepData.Duration : ''}" />
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(stepDiv);
            updateFlowStepsJson();
        }

        function removeFlowStep(id) {
            const element = document.getElementById(`flowStep_${id}`);
            if (element) {
                element.remove();
                updateFlowStepsJson();
            }
        }

        function updateFlowStepsJson() {
            const steps = [];
            document.querySelectorAll('[id^="flowStep_"]').forEach(stepDiv => {
                const step = parseInt(stepDiv.querySelector('.flow-step-number').value) || 1;
                const action = stepDiv.querySelector('.flow-step-action').value;
                const responsible = stepDiv.querySelector('.flow-step-responsible').value;
                const duration = stepDiv.querySelector('.flow-step-duration').value;

                if (action || responsible || duration) {
                    steps.push({ Step: step, Action: action, Responsible: responsible, Duration: duration });
                }
            });
            document.getElementById('flowStepsJson').value = JSON.stringify(steps);
        }

        // RACI Functions
        function addRaciItem(raciData = null) {
            raciCounter++;
            const container = document.getElementById('raciContainer');
            const raciDiv = document.createElement('div');
            raciDiv.className = 'card mb-3';
            raciDiv.id = `raci_${raciCounter}`;
            raciDiv.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">RACI Item ${raciCounter}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRaciItem(${raciCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Task</label>
                            <input type="text" class="form-control raci-task" placeholder="Task description" value="${raciData ? raciData.Task : ''}" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Responsible</label>
                            <input type="text" class="form-control raci-responsible" placeholder="Who does the work" value="${raciData ? raciData.Responsible : ''}" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Accountable</label>
                            <input type="text" class="form-control raci-accountable" placeholder="Who is accountable" value="${raciData ? raciData.Accountable : ''}" />
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(raciDiv);
            updateRaciJson();
        }

        function removeRaciItem(id) {
            const element = document.getElementById(`raci_${id}`);
            if (element) {
                element.remove();
                updateRaciJson();
            }
        }

        function updateRaciJson() {
            const racis = [];
            document.querySelectorAll('[id^="raci_"]').forEach(raciDiv => {
                const task = raciDiv.querySelector('.raci-task').value;
                const responsible = raciDiv.querySelector('.raci-responsible').value;
                const accountable = raciDiv.querySelector('.raci-accountable').value;

                if (task || responsible || accountable) {
                    racis.push({ Task: task, Responsible: responsible, Accountable: accountable });
                }
            });
            document.getElementById('raciJson').value = JSON.stringify(racis);
        }

        // KPI Functions
        function addKpi(kpiData = null) {
            kpiCounter++;
            const container = document.getElementById('kpisContainer');
            const kpiDiv = document.createElement('div');
            kpiDiv.className = 'card mb-3';
            kpiDiv.id = `kpi_${kpiCounter}`;
            kpiDiv.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">KPI ${kpiCounter}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeKpi(${kpiCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">KPI Name</label>
                            <input type="text" class="form-control kpi-name" placeholder="KPI name" value="${kpiData ? kpiData.Name : ''}" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Type</label>
                            <select class="form-control kpi-type">
                                <option value="">Select type</option>
                                <option value="Percentage" ${kpiData && kpiData.Type === 'Percentage' ? 'selected' : ''}>Percentage</option>
                                <option value="Duration" ${kpiData && kpiData.Type === 'Duration' ? 'selected' : ''}>Duration</option>
                                <option value="Rating" ${kpiData && kpiData.Type === 'Rating' ? 'selected' : ''}>Rating</option>
                                <option value="Count" ${kpiData && kpiData.Type === 'Count' ? 'selected' : ''}>Count</option>
                                <option value="Currency" ${kpiData && kpiData.Type === 'Currency' ? 'selected' : ''}>Currency</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Target Value</label>
                            <input type="text" class="form-control kpi-value" placeholder="Target value" value="${kpiData ? kpiData.Value : ''}" />
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(kpiDiv);
            updateKpisJson();
        }

        function removeKpi(id) {
            const element = document.getElementById(`kpi_${id}`);
            if (element) {
                element.remove();
                updateKpisJson();
            }
        }

        function updateKpisJson() {
            const kpis = [];
            document.querySelectorAll('[id^="kpi_"]').forEach(kpiDiv => {
                const name = kpiDiv.querySelector('.kpi-name').value;
                const type = kpiDiv.querySelector('.kpi-type').value;
                const value = kpiDiv.querySelector('.kpi-value').value;

                if (name || type || value) {
                    kpis.push({ Name: name, Type: type, Value: value });
                }
            });
            document.getElementById('kpisJson').value = JSON.stringify(kpis);
        }

        // Form Functions
        function addForm(formData = null) {
            formCounter++;
            const container = document.getElementById('formsContainer');
            const formDiv = document.createElement('div');
            formDiv.className = 'card mb-3';
            formDiv.id = `form_${formCounter}`;
            formDiv.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Form ${formCounter}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeForm(${formCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Form Name</label>
                            <input type="text" class="form-control form-name" placeholder="Form name" value="${formData ? formData.Name : ''}" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Form Code</label>
                            <input type="text" class="form-control form-code" placeholder="e.g., HR-F-001" value="${formData ? formData.Code : ''}" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Retention Period</label>
                            <input type="text" class="form-control form-retention" placeholder="e.g., 7 years" value="${formData ? formData.Retention : ''}" />
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(formDiv);
            updateFormsJson();
        }

        function removeForm(id) {
            const element = document.getElementById(`form_${id}`);
            if (element) {
                element.remove();
                updateFormsJson();
            }
        }

        function updateFormsJson() {
            const forms = [];
            document.querySelectorAll('[id^="form_"]').forEach(formDiv => {
                const name = formDiv.querySelector('.form-name').value;
                const code = formDiv.querySelector('.form-code').value;
                const retention = formDiv.querySelector('.form-retention').value;

                if (name || code || retention) {
                    forms.push({ Name: name, Code: code, Retention: retention });
                }
            });
            document.getElementById('formsJson').value = JSON.stringify(forms);
        }

        // Load existing data
        function loadExistingData() {
            // Load Flow Steps
            const flowStepsJson = document.getElementById('flowStepsJson').value;
            if (flowStepsJson && flowStepsJson !== '[]') {
                const flowSteps = JSON.parse(flowStepsJson);
                flowSteps.forEach(step => addFlowStep(step));
            } else {
                addFlowStep(); // Add one empty step
            }

            // Load RACI
            const raciJson = document.getElementById('raciJson').value;
            if (raciJson && raciJson !== '[]') {
                const racis = JSON.parse(raciJson);
                racis.forEach(raci => addRaciItem(raci));
            } else {
                addRaciItem(); // Add one empty item
            }

            // Load KPIs
            const kpisJson = document.getElementById('kpisJson').value;
            if (kpisJson && kpisJson !== '[]') {
                const kpis = JSON.parse(kpisJson);
                kpis.forEach(kpi => addKpi(kpi));
            } else {
                addKpi(); // Add one empty KPI
            }

            // Load Forms
            const formsJson = document.getElementById('formsJson').value;
            if (formsJson && formsJson !== '[]') {
                const forms = JSON.parse(formsJson);
                forms.forEach(form => addForm(form));
            } else {
                addForm(); // Add one empty form
            }
        }

        // Event listeners for real-time updates
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('flow-step-number') ||
                e.target.classList.contains('flow-step-action') ||
                e.target.classList.contains('flow-step-responsible') ||
                e.target.classList.contains('flow-step-duration')) {
                updateFlowStepsJson();
            }

            if (e.target.classList.contains('raci-task') ||
                e.target.classList.contains('raci-responsible') ||
                e.target.classList.contains('raci-accountable')) {
                updateRaciJson();
            }

            if (e.target.classList.contains('kpi-name') ||
                e.target.classList.contains('kpi-type') ||
                e.target.classList.contains('kpi-value')) {
                updateKpisJson();
            }

            if (e.target.classList.contains('form-name') ||
                e.target.classList.contains('form-code') ||
                e.target.classList.contains('form-retention')) {
                updateFormsJson();
            }
        });

        // Initialize with existing data
        document.addEventListener('DOMContentLoaded', function() {
            loadExistingData();
        });
    </script>
}
