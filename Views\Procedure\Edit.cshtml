@model ChatbotApp.Models.ProcedureViewModel

@{
    ViewData["Title"] = "Edit Procedure";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-edit me-2"></i>
                    Edit Procedure: @Model.Procedure.Id
                </h2>
                <div>
                    <a asp-action="Details" asp-route-id="@Model.Procedure.Id" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>
                        View Details
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to List
                    </a>
                </div>
            </div>

            <form asp-action="Edit" method="post">
                <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Id" class="form-label">Procedure ID *</label>
                                    <input asp-for="Procedure.Id" class="form-control" readonly />
                                    <div class="form-text">ID cannot be changed after creation</div>
                                    <span asp-validation-for="Procedure.Id" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Payload.Version" class="form-label">Version *</label>
                                    <input asp-for="Procedure.Payload.Version" class="form-control" required />
                                    <span asp-validation-for="Procedure.Payload.Version" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Payload.Sector" class="form-label">Sector *</label>
                                    <input asp-for="Procedure.Payload.Sector" class="form-control" required />
                                    <span asp-validation-for="Procedure.Payload.Sector" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Payload.Department" class="form-label">Department *</label>
                                    <input asp-for="Procedure.Payload.Department" class="form-control" required />
                                    <span asp-validation-for="Procedure.Payload.Department" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Procedure.Payload.Section" class="form-label">Section</label>
                                    <input asp-for="Procedure.Payload.Section" class="form-control" />
                                    <span asp-validation-for="Procedure.Payload.Section" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Title" class="form-label">Title *</label>
                            <input asp-for="Procedure.Payload.Title" class="form-control" required />
                            <span asp-validation-for="Procedure.Payload.Title" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Procedure Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Objective" class="form-label">Objective</label>
                            <textarea asp-for="Procedure.Payload.Objective" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Payload.Objective" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Scope" class="form-label">Scope</label>
                            <textarea asp-for="Procedure.Payload.Scope" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Payload.Scope" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Policy" class="form-label">Policy</label>
                            <textarea asp-for="Procedure.Payload.Policy" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Payload.Policy" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Responsibility" class="form-label">Responsibility</label>
                            <textarea asp-for="Procedure.Payload.Responsibility" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Payload.Responsibility" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Definitions" class="form-label">Definitions</label>
                            <textarea asp-for="Procedure.Payload.Definitions" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Procedure.Payload.Definitions" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Procedure.Payload.Description" class="form-label">Description</label>
                            <textarea asp-for="Procedure.Payload.Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Procedure.Payload.Description" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ReferencesText" class="form-label">References</label>
                            <textarea asp-for="ReferencesText" class="form-control" rows="2" 
                                      placeholder="Enter references separated by commas"></textarea>
                            <div class="form-text">Enter multiple references separated by commas</div>
                            <span asp-validation-for="ReferencesText" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list-ol me-2"></i>
                            Advanced Sections
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="FlowStepsJson" class="form-label">Flow Steps (JSON)</label>
                                    <textarea asp-for="FlowStepsJson" class="form-control" rows="4" 
                                              placeholder='[{"Step": 1, "Action": "Action description", "Responsible": "Role", "Duration": "Time"}]'></textarea>
                                    <div class="form-text">Enter flow steps as JSON array</div>
                                    <span asp-validation-for="FlowStepsJson" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="RaciJson" class="form-label">RACI Matrix (JSON)</label>
                                    <textarea asp-for="RaciJson" class="form-control" rows="4" 
                                              placeholder='[{"Task": "Task name", "Responsible": "Role", "Accountable": "Role"}]'></textarea>
                                    <div class="form-text">Enter RACI matrix as JSON array</div>
                                    <span asp-validation-for="RaciJson" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="KpisJson" class="form-label">KPIs (JSON)</label>
                                    <textarea asp-for="KpisJson" class="form-control" rows="4" 
                                              placeholder='[{"Name": "KPI name", "Type": "Type", "Value": "Target value"}]'></textarea>
                                    <div class="form-text">Enter KPIs as JSON array</div>
                                    <span asp-validation-for="KpisJson" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="FormsJson" class="form-label">Forms (JSON)</label>
                                    <textarea asp-for="FormsJson" class="form-control" rows="4" 
                                              placeholder='[{"Name": "Form name", "Code": "Form code", "Retention": "Retention period"}]'></textarea>
                                    <div class="form-text">Enter forms as JSON array</div>
                                    <span asp-validation-for="FormsJson" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 mb-4">
                    <button type="submit" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-save me-2"></i>
                        Update Procedure
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.Procedure.Id" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-eye me-2"></i>
                        View Details
                    </a>
                    <a asp-action="Index" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
