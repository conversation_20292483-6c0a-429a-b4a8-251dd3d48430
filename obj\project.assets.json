{"version": 3, "targets": {"net9.0": {"Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "compile": {"ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.Extensions.AI/9.6.0": {"type": "package", "dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Text.Json": "9.0.6", "System.Threading.Channels": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.AI.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.AI.dll": {"related": ".xml"}}}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"type": "package", "dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.5.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Net.Compilers.Toolset/4.14.0": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"type": "package", "dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.VectorData.Abstractions": "9.7.0"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Connectors.Ollama/1.60.0-alpha": {"type": "package", "dependencies": {"Microsoft.Net.Compilers.Toolset": "4.14.0", "Microsoft.SemanticKernel.Abstractions": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0", "OllamaSharp": "5.2.3"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Core/1.60.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.SemanticKernel.Abstractions": "1.60.0", "System.Numerics.Tensors": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"related": ".xml"}}}, "OllamaSharp/5.2.3": {"type": "package", "dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.5.0"}, "compile": {"lib/net9.0/OllamaSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/OllamaSharp.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Numerics.Tensors/9.0.6": {"type": "package", "compile": {"lib/net9.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Text.Json/9.0.6": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/9.0.6": {"type": "package", "compile": {"lib/net9.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}}}, "libraries": {"Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.AI/9.6.0": {"sha512": "JrMdI7lKN23axyQpWLF2B1Pgzxo3+oO/1XNC90rlInlkdHnhOwqZ9vHlcZu5gZLtQPQLf6MbnWwgInm+GVuEpA==", "type": "package", "path": "microsoft.extensions.ai/9.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net462/Microsoft.Extensions.AI.dll", "lib/net462/Microsoft.Extensions.AI.xml", "lib/net8.0/Microsoft.Extensions.AI.dll", "lib/net8.0/Microsoft.Extensions.AI.xml", "lib/net9.0/Microsoft.Extensions.AI.dll", "lib/net9.0/Microsoft.Extensions.AI.xml", "lib/netstandard2.0/Microsoft.Extensions.AI.dll", "lib/netstandard2.0/Microsoft.Extensions.AI.xml", "microsoft.extensions.ai.9.6.0.nupkg.sha512", "microsoft.extensions.ai.nuspec"]}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"sha512": "xGO7rHg3qK8jRdriAxIrsH4voNemCf8GVmgdcPXI5gpZ6lZWqOEM4ZO8yfYxUmg7+URw2AY1h7Uc/H17g7X1Kw==", "type": "package", "path": "microsoft.extensions.ai.abstractions/9.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net462/Microsoft.Extensions.AI.Abstractions.dll", "lib/net462/Microsoft.Extensions.AI.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.xml", "microsoft.extensions.ai.abstractions.9.6.0.nupkg.sha512", "microsoft.extensions.ai.abstractions.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"sha512": "bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.6": {"sha512": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "type": "package", "path": "microsoft.extensions.primitives/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.6.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"sha512": "Vth/omSCX2vR0JabzSRU/hdPhr0CvUVZlaS2lJPWHrEwvak8ntrQLDtLMtMiWKSvviGBe/WmjUW8gA3qqn9tjw==", "type": "package", "path": "microsoft.extensions.vectordata.abstractions/9.7.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "PACKAGE.md", "icon.png", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.xml", "microsoft.extensions.vectordata.abstractions.9.7.0.nupkg.sha512", "microsoft.extensions.vectordata.abstractions.nuspec", "neticon.png"]}, "Microsoft.Net.Compilers.Toolset/4.14.0": {"sha512": "/F7wcMX7Al+nXbNII/6F4b33F3WCPL1I5VbY0+9QPiKdzlYPM0+R+nyYPBsxVZ4cwGXeU0yxrL0ITJWejsvN/Q==", "type": "package", "path": "microsoft.net.compilers.toolset/4.14.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "build/Microsoft.Net.Compilers.Toolset.props", "buildMultiTargeting/Microsoft.Net.Compilers.Toolset.props", "microsoft.net.compilers.toolset.4.14.0.nupkg.sha512", "microsoft.net.compilers.toolset.nuspec", "tasks/net472/Microsoft.Build.Tasks.CodeAnalysis.dll", "tasks/net472/Microsoft.CSharp.Core.targets", "tasks/net472/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "tasks/net472/Microsoft.CodeAnalysis.CSharp.dll", "tasks/net472/Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll", "tasks/net472/Microsoft.CodeAnalysis.Scripting.dll", "tasks/net472/Microsoft.CodeAnalysis.VisualBasic.dll", "tasks/net472/Microsoft.CodeAnalysis.dll", "tasks/net472/Microsoft.DiaSymReader.Native.amd64.dll", "tasks/net472/Microsoft.DiaSymReader.Native.arm64.dll", "tasks/net472/Microsoft.DiaSymReader.Native.x86.dll", "tasks/net472/Microsoft.Managed.Core.CurrentVersions.targets", "tasks/net472/Microsoft.Managed.Core.targets", "tasks/net472/Microsoft.VisualBasic.Core.targets", "tasks/net472/System.Buffers.dll", "tasks/net472/System.Collections.Immutable.dll", "tasks/net472/System.Memory.dll", "tasks/net472/System.Numerics.Vectors.dll", "tasks/net472/System.Reflection.Metadata.dll", "tasks/net472/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/System.Text.Encoding.CodePages.dll", "tasks/net472/System.Threading.Tasks.Extensions.dll", "tasks/net472/VBCSCompiler.exe", "tasks/net472/VBCSCompiler.exe.config", "tasks/net472/cs/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/csc.exe", "tasks/net472/csc.exe.config", "tasks/net472/csc.rsp", "tasks/net472/csi.exe", "tasks/net472/csi.exe.config", "tasks/net472/csi.rsp", "tasks/net472/de/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/es/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/fr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/it/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ja/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ko/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/pl/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/pt-BR/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ru/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/tr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/vbc.exe", "tasks/net472/vbc.exe.config", "tasks/net472/vbc.rsp", "tasks/net472/zh-Hans/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/zh-Hant/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/Microsoft.Build.Tasks.CodeAnalysis.deps.json", "tasks/netcore/Microsoft.Build.Tasks.CodeAnalysis.dll", "tasks/netcore/Microsoft.CSharp.Core.targets", "tasks/netcore/Microsoft.Managed.Core.CurrentVersions.targets", "tasks/netcore/Microsoft.Managed.Core.targets", "tasks/netcore/Microsoft.VisualBasic.Core.targets", "tasks/netcore/bincore/Microsoft.CodeAnalysis.CSharp.dll", "tasks/netcore/bincore/Microsoft.CodeAnalysis.VisualBasic.dll", "tasks/netcore/bincore/Microsoft.CodeAnalysis.dll", "tasks/netcore/bincore/VBCSCompiler.deps.json", "tasks/netcore/bincore/VBCSCompiler.dll", "tasks/netcore/bincore/VBCSCompiler.runtimeconfig.json", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/csc.deps.json", "tasks/netcore/bincore/csc.dll", "tasks/netcore/bincore/csc.runtimeconfig.json", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/vbc.deps.json", "tasks/netcore/bincore/vbc.dll", "tasks/netcore/bincore/vbc.runtimeconfig.json", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/cs/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/de/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/es/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/fr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/it/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ja/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ko/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/pl/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/pt-BR/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ru/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/tr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/zh-Hans/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/zh-Hant/Microsoft.Build.Tasks.CodeAnalysis.resources.dll"]}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"sha512": "//jUQGgpWHf3Q9cNCsa259/j2FnSNQFrnW3fLgdEZ+aRC/C727j75GjwVlAhbsDd0K2+p+x3/bEM9jVHcNrKlw==", "type": "package", "path": "microsoft.semantickernel.abstractions/1.60.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll", "lib/net8.0/Microsoft.SemanticKernel.Abstractions.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Abstractions.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Abstractions.xml", "microsoft.semantickernel.abstractions.1.60.0.nupkg.sha512", "microsoft.semantickernel.abstractions.nuspec"]}, "Microsoft.SemanticKernel.Connectors.Ollama/1.60.0-alpha": {"sha512": "Y4LXv9DldggTSoNdXbWVbi9J81nvddRnHO9sTRZyzyn1zr//7ox5NPYiFEzvHGOpLpiPMlhGhxtGRdZrupXJlA==", "type": "package", "path": "microsoft.semantickernel.connectors.ollama/1.60.0-alpha", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Ollama.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Ollama.xml", "microsoft.semantickernel.connectors.ollama.1.60.0-alpha.nupkg.sha512", "microsoft.semantickernel.connectors.ollama.nuspec"]}, "Microsoft.SemanticKernel.Core/1.60.0": {"sha512": "dE3JkvQNKYN29mTg4Fu6iwf1Ao51jrbReudqEoQhGePcsDBsjQh7YVs6PM4zwUtZzdMeKfkEF4n39HQEz86oXw==", "type": "package", "path": "microsoft.semantickernel.core/1.60.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Core.dll", "lib/net8.0/Microsoft.SemanticKernel.Core.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Core.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Core.xml", "microsoft.semantickernel.core.1.60.0.nupkg.sha512", "microsoft.semantickernel.core.nuspec"]}, "OllamaSharp/5.2.3": {"sha512": "bFRAhOXSeigi0Qm9RHOStEDaXiV4bZKZxJNy1v1SOQ/W8X0OGctOmCRqe7ApbVGTAIyD7CPFWhum+eIDfOP75w==", "type": "package", "path": "ollamasharp/5.2.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "Ollama.png", "README.md", "analyzers/dotnet/cs/OllamaSharp.SourceGenerators.dll", "lib/net8.0/OllamaSharp.dll", "lib/net8.0/OllamaSharp.xml", "lib/net9.0/OllamaSharp.dll", "lib/net9.0/OllamaSharp.xml", "lib/netstandard2.0/OllamaSharp.dll", "lib/netstandard2.0/OllamaSharp.xml", "lib/netstandard2.1/OllamaSharp.dll", "lib/netstandard2.1/OllamaSharp.xml", "ollamasharp.5.2.3.nupkg.sha512", "ollamasharp.nuspec"]}, "System.Diagnostics.DiagnosticSource/9.0.6": {"sha512": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Tensors/9.0.6": {"sha512": "NOLvZVal7jhuhmLFNuMQnCUclSAEvemJlwjyBxoa8CeK6Oj8326bM4AqB2dcH+8FGna3X3ZtP4PCLrIScyddtA==", "type": "package", "path": "system.numerics.tensors/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Numerics.Tensors.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Numerics.Tensors.targets", "lib/net462/System.Numerics.Tensors.dll", "lib/net462/System.Numerics.Tensors.xml", "lib/net8.0/System.Numerics.Tensors.dll", "lib/net8.0/System.Numerics.Tensors.xml", "lib/net9.0/System.Numerics.Tensors.dll", "lib/net9.0/System.Numerics.Tensors.xml", "lib/netstandard2.0/System.Numerics.Tensors.dll", "lib/netstandard2.0/System.Numerics.Tensors.xml", "system.numerics.tensors.9.0.6.nupkg.sha512", "system.numerics.tensors.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.6": {"sha512": "h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "type": "package", "path": "system.text.json/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.6.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/9.0.6": {"sha512": "2MaNJVkG2yJiXQbZrrcYoJ55ehV+aX0zqR6rWJkO/Qj7jTsArWthrQ7iWywUf/sE5ylJWX/iLH2kKfwSRdkWsA==", "type": "package", "path": "system.threading.channels/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Channels.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "lib/net462/System.Threading.Channels.dll", "lib/net462/System.Threading.Channels.xml", "lib/net8.0/System.Threading.Channels.dll", "lib/net8.0/System.Threading.Channels.xml", "lib/net9.0/System.Threading.Channels.dll", "lib/net9.0/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.9.0.6.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.SemanticKernel.Connectors.Ollama >= 1.60.0-alpha"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\ChatbotApp.csproj", "projectName": "ChatbotApp", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\ChatbotApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.SemanticKernel.Connectors.Ollama": {"target": "Package", "version": "[1.60.0-alpha, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}