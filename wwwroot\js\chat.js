// SignalR connection
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/chatHub")
    .withAutomaticReconnect()
    .build();

// DOM elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const chatContainer = document.getElementById('chatContainer');
const typingIndicator = document.getElementById('typingIndicator');
const connectionStatus = document.getElementById('connectionStatus');

let currentBotMessage = null;
let isReceivingResponse = false;

// Start the connection
connection.start().then(function () {
    console.log('SignalR Connected');
    updateConnectionStatus('Connected', 'success');
    enableInput();
}).catch(function (err) {
    console.error('SignalR Connection Error: ', err);
    updateConnectionStatus('Disconnected', 'danger');
    disableInput();
});

// Connection events
connection.onreconnecting(() => {
    updateConnectionStatus('Reconnecting...', 'warning');
    disableInput();
});

connection.onreconnected(() => {
    updateConnectionStatus('Connected', 'success');
    enableInput();
});

connection.onclose(() => {
    updateConnectionStatus('Disconnected', 'danger');
    disableInput();
});

// SignalR event handlers
connection.on("ReceiveMessage", function (user, message) {
    addMessage(user, message, user === 'User' ? 'user-message' : 'bot-message');
});

connection.on("StartBotResponse", function () {
    isReceivingResponse = true;
    showTypingIndicator();
    currentBotMessage = createBotMessage();
    disableInput();
});

connection.on("ReceiveChunk", function (chunk) {
    if (currentBotMessage) {
        appendToMessage(currentBotMessage, chunk);
        scrollToBottom();
    }
});

connection.on("EndBotResponse", function () {
    isReceivingResponse = false;
    hideTypingIndicator();
    currentBotMessage = null;
    enableInput();
    messageInput.focus();
});

connection.on("ReceiveError", function (error) {
    isReceivingResponse = false;
    hideTypingIndicator();
    addMessage('System', `Error: ${error}`, 'bot-message text-danger');
    currentBotMessage = null;
    enableInput();
});

// UI functions
function updateConnectionStatus(status, type) {
    connectionStatus.innerHTML = `<i class="fas fa-wifi me-1"></i>${status}`;
    connectionStatus.className = `badge bg-${type} ms-2`;
}

function enableInput() {
    if (!isReceivingResponse) {
        messageInput.disabled = false;
        sendButton.disabled = false;
    }
}

function disableInput() {
    messageInput.disabled = true;
    sendButton.disabled = true;
}

function showTypingIndicator() {
    typingIndicator.style.display = 'block';
}

function hideTypingIndicator() {
    typingIndicator.style.display = 'none';
}

function addMessage(user, message, className) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${className}`;
    
    if (user === 'User') {
        messageDiv.innerHTML = `<strong>You:</strong> ${escapeHtml(message)}`;
    } else {
        messageDiv.innerHTML = `<strong>${escapeHtml(user)}:</strong> ${escapeHtml(message)}`;
    }
    
    chatContainer.appendChild(messageDiv);
    scrollToBottom();
}

function createBotMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message bot-message';
    messageDiv.innerHTML = '<strong>Assistant:</strong> ';
    
    const contentSpan = document.createElement('span');
    messageDiv.appendChild(contentSpan);
    
    chatContainer.appendChild(messageDiv);
    scrollToBottom();
    
    return contentSpan;
}

function appendToMessage(messageElement, chunk) {
    messageElement.textContent += chunk;
}

function scrollToBottom() {
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message && !isReceivingResponse) {
        connection.invoke("SendMessage", message).catch(function (err) {
            console.error('Error sending message: ', err);
            addMessage('System', 'Failed to send message. Please try again.', 'bot-message text-danger');
        });
        messageInput.value = '';
    }
}

// Event listeners
sendButton.addEventListener('click', sendMessage);

messageInput.addEventListener('keypress', function (e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// Focus on input when page loads
document.addEventListener('DOMContentLoaded', function() {
    messageInput.focus();
});
