// SignalR connection
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/chatHub")
    .withAutomaticReconnect()
    .build();

// DOM elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const chatContainer = document.getElementById('chatContainer');
const typingIndicator = document.getElementById('typingIndicator');
const connectionStatus = document.getElementById('connectionStatus');

let currentBotMessage = null;
let isReceivingResponse = false;
let currentBotContent = ''; // Store the raw content for markdown parsing

// Start the connection
connection.start().then(function () {
    console.log('SignalR Connected');
    updateConnectionStatus('Connected', 'success');
    enableInput();
}).catch(function (err) {
    console.error('SignalR Connection Error: ', err);
    updateConnectionStatus('Disconnected', 'danger');
    disableInput();
});

// Connection events
connection.onreconnecting(() => {
    updateConnectionStatus('Reconnecting...', 'warning');
    disableInput();
});

connection.onreconnected(() => {
    updateConnectionStatus('Connected', 'success');
    enableInput();
});

connection.onclose(() => {
    updateConnectionStatus('Disconnected', 'danger');
    disableInput();
});

// SignalR event handlers
connection.on("ReceiveMessage", function (user, message) {
    addMessage(user, message, user === 'User' ? 'user-message' : 'bot-message');
});

connection.on("StartBotResponse", function () {
    isReceivingResponse = true;
    showTypingIndicator();
    currentBotMessage = createBotMessage();
    currentBotContent = ''; // Reset content
    disableInput();
});

connection.on("ReceiveChunk", function (chunk) {
    if (currentBotMessage) {
        currentBotContent += chunk;
        renderMarkdownContent(currentBotMessage, currentBotContent);
        scrollToBottom();
    }
});

connection.on("EndBotResponse", function () {
    isReceivingResponse = false;
    hideTypingIndicator();
    // Final render of the complete message
    if (currentBotMessage && currentBotContent) {
        renderMarkdownContent(currentBotMessage, currentBotContent);
    }
    currentBotMessage = null;
    currentBotContent = '';
    enableInput();
    messageInput.focus();
});

connection.on("ReceiveError", function (error) {
    isReceivingResponse = false;
    hideTypingIndicator();
    addMessage('System', `Error: ${error}`, 'bot-message text-danger');
    currentBotMessage = null;
    enableInput();
});

// UI functions
function updateConnectionStatus(status, type) {
    connectionStatus.innerHTML = `<i class="fas fa-wifi me-1"></i>${status}`;
    connectionStatus.className = `badge bg-${type} ms-2`;
}

function enableInput() {
    if (!isReceivingResponse) {
        messageInput.disabled = false;
        sendButton.disabled = false;
    }
}

function disableInput() {
    messageInput.disabled = true;
    sendButton.disabled = true;
}

function showTypingIndicator() {
    typingIndicator.style.display = 'block';
}

function hideTypingIndicator() {
    typingIndicator.style.display = 'none';
}

function addMessage(user, message, className) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${className}`;

    if (user === 'User') {
        messageDiv.innerHTML = `<strong>You:</strong> ${escapeHtml(message)}`;
    } else if (user === 'System') {
        messageDiv.innerHTML = `<strong>${escapeHtml(user)}:</strong> ${escapeHtml(message)}`;
    } else {
        // For bot messages, render markdown
        const userLabel = document.createElement('strong');
        userLabel.textContent = `${user}: `;
        messageDiv.appendChild(userLabel);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        renderMarkdownContent(contentDiv, message);
        messageDiv.appendChild(contentDiv);
    }

    chatContainer.appendChild(messageDiv);
    scrollToBottom();
}

function createBotMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message bot-message';

    const userLabel = document.createElement('strong');
    userLabel.textContent = 'Assistant: ';
    messageDiv.appendChild(userLabel);

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    messageDiv.appendChild(contentDiv);

    chatContainer.appendChild(messageDiv);
    scrollToBottom();

    return contentDiv;
}

function renderMarkdownContent(element, content) {
    try {
        // Configure marked options for better rendering
        marked.setOptions({
            breaks: true,        // Convert \n to <br>
            gfm: true,          // GitHub Flavored Markdown
            sanitize: false,    // We'll use DOMPurify instead
            smartLists: true,
            smartypants: true
        });

        // Parse markdown to HTML
        let html = marked.parse(content);

        // Sanitize HTML to prevent XSS attacks
        if (typeof DOMPurify !== 'undefined') {
            html = DOMPurify.sanitize(html, {
                ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'code', 'pre', 'blockquote',
                              'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li',
                              'a', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td'],
                ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class']
            });
        }

        element.innerHTML = html;
    } catch (error) {
        console.error('Error rendering markdown:', error);
        // Fallback to plain text
        element.textContent = content;
    }
}

function scrollToBottom() {
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message && !isReceivingResponse) {
        connection.invoke("SendMessage", message).catch(function (err) {
            console.error('Error sending message: ', err);
            addMessage('System', 'Failed to send message. Please try again.', 'bot-message text-danger');
        });
        messageInput.value = '';
    }
}

// Event listeners
sendButton.addEventListener('click', sendMessage);

messageInput.addEventListener('keypress', function (e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// Focus on input when page loads
document.addEventListener('DOMContentLoaded', function() {
    messageInput.focus();
});
