using ChatbotApp.Services.Interfaces;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using System.Runtime.CompilerServices;

namespace ChatbotApp.Services;

using ChatbotApp.Models;

public class OllamaService : IOllamaService
{
    //private readonly Kernel _kernel;
    private readonly Kernel _kernel;
    public Kernel Kernel => _kernel;
    private readonly ILogger<OllamaService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IProcedureVectorService _procedureVectorService;
    private const string DefaultModel = "qwen3:14b";
    private const string OllamaBaseUrl = "http://localhost:11434";

    public OllamaService(ILogger<OllamaService> logger, Kernel kernel, IProcedureVectorService procedureVectorService)
    {
        _logger = logger;
        _httpClient = new HttpClient();
        _procedureVectorService = procedureVectorService;

        // Create Semantic Kernel with Ollama connector
        //var builder = Kernel.CreateBuilder();
        //builder.AddOllamaChatCompletion(
        //    modelId: DefaultModel,
        //    endpoint: new Uri(OllamaBaseUrl));

        //_kernel = builder.Build();

        _kernel = kernel;
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{OllamaBaseUrl}/api/tags");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Ollama service is not available");
            return false;
        }
    }

    public async IAsyncEnumerable<string> GenerateStreamAsync(string prompt, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        IChatCompletionService? chatCompletionService = null;
        ChatHistory? chatHistory = null;
        bool hasError = false;
        string? errorMessage = null;

        try
        {
            chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();
            chatHistory = new ChatHistory();

            // Search for relevant procedures
            var procedureContext = await GetProcedureContextAsync(prompt);

            // Create enhanced prompt with procedure context
            var enhancedPrompt = CreateEnhancedPrompt(prompt, procedureContext);

            chatHistory.AddUserMessage(enhancedPrompt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while initializing Semantic Kernel chat service");
            hasError = true;
            errorMessage = ex.Message;
        }

        if (hasError)
        {
            yield return $"Error: {errorMessage}";
            yield break;
        }

        IAsyncEnumerable<StreamingChatMessageContent>? streamingResponse = null;
        bool streamError = false;
        string? streamErrorMessage = null;

        try
        {
            streamingResponse = chatCompletionService!.GetStreamingChatMessageContentsAsync(
                chatHistory!,
                cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while starting stream from Ollama via Semantic Kernel");
            streamError = true;
            streamErrorMessage = ex.Message;
        }

        if (streamError)
        {
            yield return $"Error: {streamErrorMessage}";
            yield break;
        }

        await foreach (var content in streamingResponse!)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            if (content.Content != null)
            {
                yield return content.Content;
            }
        }
    }

    private async Task<List<ProcedureSearchResult>> GetProcedureContextAsync(string prompt)
    {
        try
        {
            // Search for relevant procedures based on the user's prompt
            var results = await _procedureVectorService.SearchProceduresAsync(prompt, topK: 3, minScore: 0.6f);
            _logger.LogInformation("Found {Count} relevant procedures for prompt", results.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error searching procedures for context");
            return new List<ProcedureSearchResult>();
        }
    }

    private string CreateEnhancedPrompt(string originalPrompt, List<ProcedureSearchResult> procedureContext)
    {
        if (!procedureContext.Any())
        {
            return originalPrompt;
        }

        var contextBuilder = new System.Text.StringBuilder();
        contextBuilder.AppendLine("Based on the following organizational procedures, please answer the user's question:");
        contextBuilder.AppendLine();

        foreach (var procedure in procedureContext)
        {
            contextBuilder.AppendLine($"**Procedure: {procedure.Title} (ID: {procedure.Id})**");
            contextBuilder.AppendLine(procedure.Content);
            contextBuilder.AppendLine();
        }

        contextBuilder.AppendLine("---");
        contextBuilder.AppendLine();
        contextBuilder.AppendLine("User Question:");
        contextBuilder.AppendLine(originalPrompt);
        contextBuilder.AppendLine();
        contextBuilder.AppendLine("Please provide a comprehensive answer based on the procedures above. If the question is not related to any of the procedures, please respond normally but mention that no specific procedures were found for this topic.");

        return contextBuilder.ToString();
    }
}
