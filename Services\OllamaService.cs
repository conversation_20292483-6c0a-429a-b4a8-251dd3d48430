using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;

namespace ChatbotApp.Services;

public class OllamaService : IOllamaService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OllamaService> _logger;
    private const string DefaultModel = "qwen2.5:14b";
    private const string OllamaBaseUrl = "http://localhost:11434";

    public OllamaService(HttpClient httpClient, ILogger<OllamaService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _httpClient.BaseAddress = new Uri(OllamaBaseUrl);
        _httpClient.Timeout = TimeSpan.FromMinutes(5); // Long timeout for AI responses
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/tags");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Ollama service is not available");
            return false;
        }
    }

    public Task<IAsyncEnumerable<string>> GenerateStreamAsync(string prompt, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(GenerateStreamInternalAsync(prompt, cancellationToken));
    }

    private async IAsyncEnumerable<string> GenerateStreamInternalAsync(
        string prompt,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var requestBody = new
        {
            model = DefaultModel,
            prompt = prompt,
            stream = true
        };

        var json = JsonSerializer.Serialize(requestBody);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        HttpResponseMessage? response = null;
        Stream? stream = null;
        StreamReader? reader = null;
        bool hasError = false;
        string? errorMessage = null;

        try
        {
            response = await _httpClient.PostAsync("/api/generate", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            reader = new StreamReader(stream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while connecting to Ollama");
            hasError = true;
            errorMessage = ex.Message;
        }

        if (hasError)
        {
            yield return $"Error: {errorMessage}";
            yield break;
        }

        try
        {
            string? line;
            while ((line = await reader!.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
            {
                if (string.IsNullOrWhiteSpace(line))
                    continue;

                var result = ParseJsonLine(line);
                if (!string.IsNullOrEmpty(result.ResponseText))
                {
                    yield return result.ResponseText;
                }

                if (result.IsDone)
                {
                    break;
                }
            }
        }
        finally
        {
            reader?.Dispose();
            stream?.Dispose();
            response?.Dispose();
        }
    }

    private (string? ResponseText, bool IsDone) ParseJsonLine(string line)
    {
        try
        {
            using var jsonDoc = JsonDocument.Parse(line);
            var root = jsonDoc.RootElement;

            string? responseText = null;
            bool isDone = false;

            if (root.TryGetProperty("response", out var responseElement))
            {
                responseText = responseElement.GetString();
            }

            if (root.TryGetProperty("done", out var doneElement))
            {
                isDone = doneElement.GetBoolean();
            }

            return (responseText, isDone);
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "Failed to parse JSON response: {Line}", line);
            return (null, false);
        }
    }
}
