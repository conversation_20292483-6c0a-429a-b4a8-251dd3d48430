using ChatbotApp.Services.Interfaces;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using System.Runtime.CompilerServices;

namespace ChatbotApp.Services;

public class OllamaService : IOllamaService
{
    //private readonly Kernel _kernel;
    private readonly Kernel _kernel;
    public Kernel Kernel => _kernel;
    private readonly ILogger<OllamaService> _logger;
    private readonly HttpClient _httpClient;
    private const string DefaultModel = "qwen3:14b";
    private const string OllamaBaseUrl = "http://localhost:11434";

    public OllamaService(ILogger<OllamaService> logger, Kernel kernel)
    {
        _logger = logger;
        _httpClient = new HttpClient();

        // Create Semantic Kernel with Ollama connector
        //var builder = Kernel.CreateBuilder();
        //builder.AddOllamaChatCompletion(
        //    modelId: DefaultModel,
        //    endpoint: new Uri(OllamaBaseUrl));

        //_kernel = builder.Build();

        _kernel = kernel;
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{OllamaBaseUrl}/api/tags");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Ollama service is not available");
            return false;
        }
    }

    public async IAsyncEnumerable<string> GenerateStreamAsync(string prompt, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        IChatCompletionService? chatCompletionService = null;
        ChatHistory? chatHistory = null;
        bool hasError = false;
        string? errorMessage = null;

        try
        {
            chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();
            chatHistory = new ChatHistory();
            chatHistory.AddUserMessage(prompt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while initializing Semantic Kernel chat service");
            hasError = true;
            errorMessage = ex.Message;
        }

        if (hasError)
        {
            yield return $"Error: {errorMessage}";
            yield break;
        }

        IAsyncEnumerable<StreamingChatMessageContent>? streamingResponse = null;
        bool streamError = false;
        string? streamErrorMessage = null;

        try
        {
            streamingResponse = chatCompletionService!.GetStreamingChatMessageContentsAsync(
                chatHistory!,
                cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while starting stream from Ollama via Semantic Kernel");
            streamError = true;
            streamErrorMessage = ex.Message;
        }

        if (streamError)
        {
            yield return $"Error: {streamErrorMessage}";
            yield break;
        }

        await foreach (var content in streamingResponse!)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            if (content.Content != null)
            {
                yield return content.Content;
            }
        }
    }
}
