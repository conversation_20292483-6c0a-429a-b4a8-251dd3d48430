{"Version": 1, "Hash": "R7KYsen691YecQ87dVE7gB/2vB7HW5dYakWLahwS4V8=", "Source": "ChatbotApp", "BasePath": "_content/ChatbotApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ChatbotApp\\wwwroot", "Source": "ChatbotApp", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint=5g4imy9nsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m895dfzpr7", "Integrity": "+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "FileLength": 1350, "LastWriteTime": "2025-07-17T20:00:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04+00:00"}], "Endpoints": [{"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js.gz"}, {"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}]}