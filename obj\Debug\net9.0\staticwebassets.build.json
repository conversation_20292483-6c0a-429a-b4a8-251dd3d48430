{"Version": 1, "Hash": "zv5qQ1+x5O2THvs3+8yhb+/AdvlR77KyOlsnkqMpva4=", "Source": "ChatbotApp", "BasePath": "_content/ChatbotApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ChatbotApp\\wwwroot", "Source": "ChatbotApp", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint=5g4imy9nsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m895dfzpr7", "Integrity": "+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "FileLength": 1350, "LastWriteTime": "2025-07-17T20:00:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/FIN-03#[.{fingerprint=wi72cewi0b}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu6m908vdq", "Integrity": "yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "FileLength": 1138, "LastWriteTime": "2025-07-18T18:05:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint=awmycvggfz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5v3pw8xpx3", "Integrity": "0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "FileLength": 2466, "LastWriteTime": "2025-07-17T21:13:27+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/HR-07#[.{fingerprint=if7imjr4iq}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5vx2ncb4h9", "Integrity": "DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "FileLength": 1279, "LastWriteTime": "2025-07-18T18:05:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/FIN-03#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wi72cewi0b", "Integrity": "aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\data\\FIN-03.json", "FileLength": 3709, "LastWriteTime": "2025-07-18T18:01:34+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/HR-07#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "if7imjr4iq", "Integrity": "HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\data\\HR-07.json", "FileLength": 4006, "LastWriteTime": "2025-07-18T18:01:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "files/PR_1#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "14askf0i1a", "Integrity": "zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\files\\PR_1.docx", "FileLength": 140853, "LastWriteTime": "2025-07-17T18:48:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "awmycvggfz", "Integrity": "lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\documents.js", "FileLength": 9158, "LastWriteTime": "2025-07-17T21:09:36+00:00"}], "Endpoints": [{"Route": "data/FIN-03.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000877963126"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3709"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.json.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw="}]}, {"Route": "data/FIN-03.wi72cewi0b.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000877963126"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "label", "Value": "data/FIN-03.json"}, {"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.wi72cewi0b.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3709"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "label", "Value": "data/FIN-03.json"}, {"Name": "integrity", "Value": "sha256-aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg="}]}, {"Route": "data/FIN-03.wi72cewi0b.json.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1138"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi72cewi0b"}, {"Name": "label", "Value": "data/FIN-03.json.gz"}, {"Name": "integrity", "Value": "sha256-yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw="}]}, {"Route": "data/HR-07.if7imjr4iq.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000781250000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "label", "Value": "data/HR-07.json"}, {"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.if7imjr4iq.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "label", "Value": "data/HR-07.json"}, {"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.if7imjr4iq.json.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "if7imjr4iq"}, {"Name": "label", "Value": "data/HR-07.json.gz"}, {"Name": "integrity", "Value": "sha256-DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8="}]}, {"Route": "data/HR-07.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000781250000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.json", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:01:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI="}]}, {"Route": "data/HR-07.json.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1279"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 18:05:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8="}]}, {"Route": "files/PR_1.14askf0i1a.docx", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "14askf0i1a"}, {"Name": "label", "Value": "files/PR_1.docx"}, {"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}]}, {"Route": "files/PR_1.docx", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js.gz"}, {"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.awmycvggfz.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js.gz"}, {"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}]}, {"Route": "js/documents.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}]}]}