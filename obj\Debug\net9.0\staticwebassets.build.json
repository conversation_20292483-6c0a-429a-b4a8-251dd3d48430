{"Version": 1, "Hash": "vGOxGeWr02j+YshsET4qt2i8U6biHJv1KWsywyTQ4Bo=", "Source": "ChatbotApp", "BasePath": "_content/ChatbotApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ChatbotApp\\wwwroot", "Source": "ChatbotApp", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint=5g4imy9nsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m895dfzpr7", "Integrity": "+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "FileLength": 1350, "LastWriteTime": "2025-07-17T20:00:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint=awmycvggfz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5v3pw8xpx3", "Integrity": "0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "FileLength": 2466, "LastWriteTime": "2025-07-17T21:13:27+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "files/PR_1#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "14askf0i1a", "Integrity": "zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\files\\PR_1.docx", "FileLength": 140853, "LastWriteTime": "2025-07-17T18:48:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "awmycvggfz", "Integrity": "lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\documents.js", "FileLength": 9158, "LastWriteTime": "2025-07-17T21:09:36+00:00"}], "Endpoints": [{"Route": "files/PR_1.14askf0i1a.docx", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "14askf0i1a"}, {"Name": "label", "Value": "files/PR_1.docx"}, {"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}]}, {"Route": "files/PR_1.docx", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140853"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:48:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js"}, {"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.5g4imy9nsy.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5g4imy9nsy"}, {"Name": "label", "Value": "js/chat.js.gz"}, {"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740192450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 19:58:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU="}]}, {"Route": "js/chat.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1350"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 20:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE="}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.awmycvggfz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js"}, {"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.awmycvggfz.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "awmycvggfz"}, {"Name": "label", "Value": "js/documents.js.gz"}, {"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}]}, {"Route": "js/documents.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405350628"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9158"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:09:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0="}]}, {"Route": "js/documents.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2466"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 21:13:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I="}]}]}