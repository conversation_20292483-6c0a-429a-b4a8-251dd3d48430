@model IEnumerable<ChatbotApp.Models.Procedure>

@{
    ViewData["Title"] = "Procedures Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-clipboard-list me-2"></i>
                    Procedures Management
                </h2>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="uploadAllToVector()">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        Upload All to Vector DB
                    </button>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create New Procedure
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        All Procedures (@Model.Count())
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Sector</th>
                                        <th>Department</th>
                                        <th>Section</th>
                                        <th>Version</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">@item.Id</span>
                                            </td>
                                            <td>
                                                <strong>@item.Title</strong>
                                            </td>
                                            <td>@item.Sector</td>
                                            <td>@item.Department</td>
                                            <td>@item.Section</td>
                                            <td>
                                                <span class="badge bg-info">@item.Version</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-success"
                                                            onclick="uploadToVector('@item.Id')" title="Upload to Vector DB">
                                                        <i class="fas fa-cloud-upload-alt"></i>
                                                    </button>
                                                    <a asp-action="Delete" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Procedures Found</h4>
                            <p class="text-muted">Start by creating your first procedure.</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Create First Procedure
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Upload single procedure to vector database
        function uploadToVector(procedureId) {
            if (!confirm(`Upload procedure ${procedureId} to vector database?`)) {
                return;
            }

            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            fetch(`/Procedure/UploadToVector?id=${encodeURIComponent(procedureId)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while uploading to vector database');
            })
            .finally(() => {
                button.innerHTML = originalHtml;
                button.disabled = false;
            });
        }

        // Upload all procedures to vector database
        function uploadAllToVector() {
            if (!confirm('Upload all procedures to vector database? This may take a while.')) {
                return;
            }

            const button = event.target;
            const originalHtml = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
            button.disabled = true;

            fetch('/Procedure/UploadAllToVector', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while uploading to vector database');
            })
            .finally(() => {
                button.innerHTML = originalHtml;
                button.disabled = false;
            });
        }

        // Show alert message
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Remove existing alerts
            document.querySelectorAll('.alert').forEach(alert => alert.remove());

            // Add new alert
            const container = document.querySelector('.container-fluid .row .col-12');
            const alertDiv = document.createElement('div');
            alertDiv.innerHTML = alertHtml;
            container.insertBefore(alertDiv.firstElementChild, container.children[1]);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                document.querySelector('.alert')?.remove();
            }, 5000);
        }

        // Check vector service status on page load
        document.addEventListener('DOMContentLoaded', function() {
            fetch('/Procedure/VectorStatus')
                .then(response => response.json())
                .then(data => {
                    if (!data.vectorServiceAvailable) {
                        showAlert('warning', 'Vector service is not available. Upload functionality may not work.');
                    }
                })
                .catch(error => {
                    console.warn('Could not check vector service status:', error);
                });
        });
    </script>
}
