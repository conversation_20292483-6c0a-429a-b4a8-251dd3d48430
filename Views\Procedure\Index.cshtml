@model IEnumerable<ChatbotApp.Models.ProcedureVectorItem>

@{
    ViewData["Title"] = "Procedures Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-clipboard-list me-2"></i>
                    Procedures Management
                </h2>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create New Procedure
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        All Procedures (@Model.Count())
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Sector</th>
                                        <th>Department</th>
                                        <th>Section</th>
                                        <th>Version</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">@item.Id</span>
                                            </td>
                                            <td>
                                                <strong>@item.Payload.Title</strong>
                                            </td>
                                            <td>@item.Payload.Sector</td>
                                            <td>@item.Payload.Department</td>
                                            <td>@item.Payload.Section</td>
                                            <td>
                                                <span class="badge bg-info">@item.Payload.Version</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.Id" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.Id" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Procedures Found</h4>
                            <p class="text-muted">Start by creating your first procedure.</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Create First Procedure
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
