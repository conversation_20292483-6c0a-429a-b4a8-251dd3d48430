// Document management functionality

let documentPanelVisible = false;

// Initialize document functionality
document.addEventListener('DOMContentLoaded', function() {
    checkVectorServiceStatus();
    loadDocumentList();
});

// Toggle document panel visibility
function toggleDocumentPanel() {
    const panel = document.getElementById('documentPanel');
    const chatContainer = document.querySelector('.col-md-8');
    
    if (documentPanelVisible) {
        panel.style.display = 'none';
        chatContainer.className = 'col-md-12';
        documentPanelVisible = false;
    } else {
        panel.style.display = 'block';
        chatContainer.className = 'col-md-8';
        documentPanelVisible = true;
    }
}

// Show upload modal
function showUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

// Upload document
async function uploadDocument() {
    const fileInput = document.getElementById('fileInput');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadResult = document.getElementById('uploadResult');
    const progressBar = uploadProgress.querySelector('.progress-bar');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        showUploadResult('Please select a file to upload.', 'danger');
        return;
    }
    
    const file = fileInput.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        // Show progress
        uploadProgress.style.display = 'block';
        progressBar.style.width = '0%';
        uploadResult.style.display = 'none';
        
        // Simulate progress (since we can't track actual upload progress easily)
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            progressBar.style.width = progress + '%';
            if (progress >= 90) {
                clearInterval(progressInterval);
            }
        }, 100);
        
        const response = await fetch('/api/document/upload', {
            method: 'POST',
            body: formData
        });
        
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        const result = await response.json();
        
        if (result.success) {
            showUploadResult(`Document "${result.document.fileName}" uploaded and processed successfully!`, 'success');
            fileInput.value = '';
            loadDocumentList(); // Refresh document list
            
            // Close modal after 2 seconds
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
                modal.hide();
            }, 2000);
        } else {
            showUploadResult(`Error: ${result.message}`, 'danger');
        }
    } catch (error) {
        console.error('Upload error:', error);
        showUploadResult(`Upload failed: ${error.message}`, 'danger');
    } finally {
        setTimeout(() => {
            uploadProgress.style.display = 'none';
        }, 1000);
    }
}

// Show upload result
function showUploadResult(message, type) {
    const uploadResult = document.getElementById('uploadResult');
    uploadResult.className = `alert alert-${type}`;
    uploadResult.textContent = message;
    uploadResult.style.display = 'block';
}

// Search documents
async function searchDocuments() {
    const query = document.getElementById('documentSearchInput').value.trim();
    const resultsContainer = document.getElementById('documentResults');
    
    if (!query) {
        resultsContainer.innerHTML = '<p class="text-muted small">Enter a search query to find relevant documents.</p>';
        return;
    }
    
    try {
        resultsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
        
        const response = await fetch('/api/document/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query,
                topK: 5,
                minScore: 0.5
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.results.length > 0) {
            displaySearchResults(result.results);
        } else {
            resultsContainer.innerHTML = '<p class="text-muted small">No relevant documents found.</p>';
        }
    } catch (error) {
        console.error('Search error:', error);
        resultsContainer.innerHTML = '<p class="text-danger small">Search failed. Please try again.</p>';
    }
}

// Display search results
function displaySearchResults(results) {
    const resultsContainer = document.getElementById('documentResults');
    let html = '';
    
    results.forEach(result => {
        html += `
            <div class="document-result-item" onclick="insertDocumentContext('${result.fileName}', '${escapeHtml(result.content)}')">
                <div class="d-flex justify-content-between align-items-start">
                    <strong class="text-primary">${escapeHtml(result.fileName)}</strong>
                    <span class="document-score badge bg-secondary">${(result.score * 100).toFixed(1)}%</span>
                </div>
                ${result.title ? `<div class="small text-muted">${escapeHtml(result.title)}</div>` : ''}
                <div class="document-content-preview">${escapeHtml(result.content)}</div>
            </div>
        `;
    });
    
    resultsContainer.innerHTML = html;
}

// Insert document context into chat
function insertDocumentContext(fileName, content) {
    const messageInput = document.getElementById('messageInput');
    const contextMessage = `Based on the document "${fileName}": ${content}\n\nPlease explain this in detail.`;
    messageInput.value = contextMessage;
    messageInput.focus();
}

// Load document list
async function loadDocumentList() {
    try {
        const response = await fetch('/api/document/list');
        const result = await response.json();
        
        if (result.success) {
            console.log(`Loaded ${result.documents.length} documents`);
            // You can add UI to display document list if needed
        }
    } catch (error) {
        console.error('Error loading documents:', error);
    }
}

// Check vector service status
async function checkVectorServiceStatus() {
    try {
        const response = await fetch('/api/document/status');
        const result = await response.json();
        
        const vectorStatus = document.getElementById('vectorStatus');
        if (result.success && result.status.vectorServiceAvailable) {
            vectorStatus.innerHTML = '<i class="fas fa-database me-1"></i>Vector DB Connected';
            vectorStatus.className = 'badge bg-success ms-2';
        } else {
            vectorStatus.innerHTML = '<i class="fas fa-database me-1"></i>Vector DB Disconnected';
            vectorStatus.className = 'badge bg-warning ms-2';
        }
    } catch (error) {
        console.error('Error checking vector service status:', error);
        const vectorStatus = document.getElementById('vectorStatus');
        vectorStatus.innerHTML = '<i class="fas fa-database me-1"></i>Vector DB Error';
        vectorStatus.className = 'badge bg-danger ms-2';
    }
}

// Process existing files
async function processExistingFiles() {
    try {
        const response = await fetch('/api/document/process-existing', {
            method: 'POST'
        });
        
        const result = await response.json();
        console.log('Processed existing files:', result);
        
        if (result.success) {
            loadDocumentList();
            checkVectorServiceStatus();
        }
    } catch (error) {
        console.error('Error processing existing files:', error);
    }
}

// Enhanced SignalR handlers for document search
if (typeof connection !== 'undefined') {
    connection.on("DocumentSearchResults", function (data) {
        console.log('Document search results:', data);
        if (data.results && data.results.length > 0) {
            displaySearchResults(data.results);
            if (!documentPanelVisible) {
                toggleDocumentPanel();
            }
        }
    });
}

// Allow Enter key in document search
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('documentSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchDocuments();
            }
        });
    }
});

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Auto-process existing files on page load (optional)
// Uncomment the line below if you want to automatically process files in wwwroot/files
// setTimeout(processExistingFiles, 2000);
