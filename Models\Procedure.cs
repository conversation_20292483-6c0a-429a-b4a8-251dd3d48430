﻿using System.Text.Json;

namespace ChatbotApp.Models;

// نموذج كامل للإجراء (يُحفظ في قاعدة بيانات عادية)
public class Procedure
{
    public Guid Key { get; set; } = Guid.NewGuid();    

    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Sector { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Section { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Objective { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
    public string Policy { get; set; } = string.Empty;
    public string Responsibility { get; set; } = string.Empty;
    public string Definitions { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime UpdatedDate { get; set; }

    // البيانات المعقدة
    public List<ProcedureStep> FlowSteps { get; set; } = new();
    public List<ProcedureRaci> Raci { get; set; } = new();
    public List<ProcedureKpi> Kpis { get; set; } = new();
    public List<ProcedureForm> Forms { get; set; } = new();
    public List<string> References { get; set; } = new();

    // تحويل إلى نموذج Vector للبحث
    public ProcedureVector ToVectorItem()
    {
        var searchableContent = BuildSearchableContent();

        return new ProcedureVector
        {
            Id = this.Id,
            Title = this.Title,
            Sector = this.Sector,
            Department = this.Department,
            Section = this.Section,
            Description = $"{this.Objective} {this.Scope} {this.Policy}",
            SearchableContent = searchableContent,
            MetadataJson = JsonSerializer.Serialize(new
            {
                Version = this.Version,
                Responsibility = this.Responsibility,
                Definitions = this.Definitions,
                UpdatedDate = this.UpdatedDate
            })
        };
    }

    private string BuildSearchableContent()
    {
        var content = new List<string>
        {
            Title,
            Objective,
            Scope,
            Policy,
            Responsibility,
            Definitions
        };

        // إضافة خطوات العمل
        foreach (var step in FlowSteps)
        {
            content.Add($"خطوة {step.Step}: {step.Action}");
        }

        // إضافة المسؤوليات
        foreach (var raci in Raci)
        {
            content.Add($"مهمة: {raci.Task} - مسؤول: {raci.Responsible}");
        }

        return string.Join(" ", content.Where(c => !string.IsNullOrWhiteSpace(c)));
    }
}

// Service للتعامل مع البيانات
/*
public interface IProcedureService
{
    Task<List<Procedure>> SearchProceduresAsync(string query, int maxResults = 10);
    Task<Procedure?> GetProcedureByIdAsync(string id);
    Task SaveProcedureAsync(Procedure procedure);
}
public class ProcedureService : IProcedureService
{
    private readonly IVectorStore _vectorStore;
    private readonly IProcedureRepository _procedureRepository; // قاعدة بيانات عادية
    private readonly IEmbeddingService _embeddingService;

    public ProcedureService(
        IVectorStore vectorStore,
        IProcedureRepository procedureRepository,
        IEmbeddingService embeddingService)
    {
        _vectorStore = vectorStore;
        _procedureRepository = procedureRepository;
        _embeddingService = embeddingService;
    }

    public async Task<List<Procedure>> SearchProceduresAsync(string query, int maxResults = 10)
    {
        // 1. البحث في Vector Database
        var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query);
        var vectorResults = await _vectorStore.SearchAsync(queryEmbedding, maxResults);

        // 2. جلب البيانات الكاملة من قاعدة البيانات العادية
        var procedureIds = vectorResults.Select(r => r.Id).ToList();
        var procedures = await _procedureRepository.GetByIdsAsync(procedureIds);

        return procedures;
    }

    public async Task<Procedure?> GetProcedureByIdAsync(string id)
    {
        return await _procedureRepository.GetByIdAsync(id);
    }

    public async Task SaveProcedureAsync(Procedure procedure)
    {
        // 1. حفظ في قاعدة البيانات العادية
        await _procedureRepository.SaveAsync(procedure);

        // 2. إنشاء Vector Item وحفظه في Vector Database
        var vectorItem = procedure.ToVectorItem();
        var embedding = await _embeddingService.GenerateEmbeddingAsync(vectorItem.SearchableContent);
        vectorItem.ContentEmbedding = embedding;

        await _vectorStore.UpsertAsync(vectorItem);
    }
}
*/