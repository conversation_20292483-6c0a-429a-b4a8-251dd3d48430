using ChatbotApp.Models;
using ChatbotApp.Services.Interfaces;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel;

namespace ChatbotApp.Services;

public class PrcedureVectorService1 : IPrcedureVectorService
{
    private readonly ILogger<PrcedureVectorService1> _logger;
    ///private readonly Kernel _kernel;
    private readonly Kernel _kernel;
    private IEmbeddingGenerator<string, Embedding<float>> _embeddingGenerator;
    
    private readonly Uri _ollamaEndpoint = new Uri("http://localhost:11434");
    private readonly Uri _qdrantEndpoint = new Uri("http://localhost:6333");
    
    private readonly string _chatModelId = "qwen3:14b";
    private readonly string _embeddingModelId = "nomic-embed-text";
    private VectorStoreCollection<Guid, ProcedureVector> _myCollection;
    private bool _isInitialized = false;

    // In-memory storage for demo purposes (replace with actual Qdrant client)
    private readonly List<DocumentChunk> _vectorStore = new();

    public PrcedureVectorService1(ILogger<PrcedureVectorService1> logger, Kernel kernel)
    {
        _logger = logger;        
        
        //var builder = kernel;

        //builder.AddOllamaChatCompletion(_chatModelId, _ollamaEndpoint);
        //builder.AddOllamaEmbeddingGenerator(_embeddingModelId, _ollamaEndpoint);

        //builder.Services.AddQdrantVectorStore();

        //builder.Services.AddSingleton(_ => new QdrantClient(_qdrantEndpoint));

        //_kernel = builder.Build();        
        _kernel = kernel;
    }

    public async Task InitializeAsync(string collectionName)
    {
        try
        {
            var vectorStore = _kernel.Services.GetRequiredService<VectorStore>();
            _embeddingGenerator = _kernel.Services.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();

            // Check if Qdrant is available
            var collections = vectorStore.ListCollectionNamesAsync();
            var collectionList = new HashSet<string>();
            await foreach (var collection in collections)
            {
                collectionList.Add(collection);
            }

            var collectionExists = collectionList.Contains(collectionName);
            _myCollection = vectorStore.GetCollection<Guid, ProcedureVector>(collectionName);
            if (!collectionExists)
            {
                await _myCollection.EnsureCollectionExistsAsync();


                _isInitialized = false;

                
            }
            _isInitialized = true;
        }
        catch (Exception ex)
        {

            _logger.LogWarning($"Initialize Qdrant service not available {ex.Message}");
        }
    }

    public async Task<bool> CreateCollectionIfNotExistsAsync(string collectionName , List<ProcedureVector> data)
    {
        try
        {

            var tasks = data.Select(entry => Task.Run(async () =>
            {
                entry.ContentEmbedding = (await _embeddingGenerator.GenerateAsync(entry.MetadataJson)).Vector;
            }));
            await Task.WhenAll(tasks);

            await _myCollection.UpsertAsync(data);

            return true;
          
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Qdrant collection");
            return false;
        }
    }



    

    public async Task<bool> IsAvailableAsync(string collectionName)
    {
        try
        {
            if (!_isInitialized)
                await InitializeAsync(collectionName);

            return _isInitialized;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Vector service is not available");
            return false;
        }
    }

    
    public async Task<IAsyncEnumerable<VectorSearchResult<ProcedureVector>>> SearchAsync(string query, int topK = 5, float minScore = 0.7F)
    {
        var queryEmbedding = await _embeddingGenerator.GenerateVectorAsync(query);
        var results = _myCollection.SearchAsync(
            queryEmbedding,
            10,
            new VectorSearchOptions<ProcedureVector>
            {
                VectorProperty = movie => movie.ContentEmbedding
            });
        return results;
    }

    
    
}
