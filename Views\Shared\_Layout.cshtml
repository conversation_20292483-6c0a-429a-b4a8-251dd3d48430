<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Ollama Chatbot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            max-width: 80%;
        }
        
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .bot-message {
            background-color: white;
            border: 1px solid #dee2e6;
            margin-right: auto;
        }
        
        .typing-indicator {
            display: none;
            font-style: italic;
            color: #6c757d;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .message-input {
            border-radius: 25px;
        }
        
        .send-button {
            border-radius: 50%;
            width: 45px;
            height: 45px;
        }

        .document-results {
            max-height: 400px;
            overflow-y: auto;
        }

        .document-result-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .document-result-item:hover {
            background-color: #f8f9fa;
        }

        .document-score {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .document-content-preview {
            font-size: 0.9rem;
            color: #495057;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-robot me-2"></i>
                    Ollama Chatbot
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Chat" asp-action="Index">
                                <i class="fas fa-comments me-1"></i>
                                Chat
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Document" asp-action="Index">
                                <i class="fas fa-file-alt me-1"></i>
                                Documents
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Procedure" asp-action="Index">
                                <i class="fas fa-clipboard-list me-1"></i>
                                Procedures
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="container mt-4">
        <main role="main">
            @RenderBody()
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
