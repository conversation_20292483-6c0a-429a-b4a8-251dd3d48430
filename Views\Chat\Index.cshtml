@{
    ViewData["Title"] = "Chat";
}

<div class="row">
    <div class="col-12">
        <div class="status-indicator">
            @if (ViewBag.OllamaAvailable)
            {
                <span class="badge bg-success">
                    <i class="fas fa-circle me-1"></i>
                    Ollama Connected
                </span>
            }
            else
            {
                <span class="badge bg-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Ollama Disconnected
                </span>
            }
            <span id="connectionStatus" class="badge bg-secondary ms-2">
                <i class="fas fa-wifi me-1"></i>
                Connecting...
            </span>
            <span id="vectorStatus" class="badge bg-secondary ms-2">
                <i class="fas fa-database me-1"></i>
                Vector DB
            </span>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    Chat with Qwen3:14b + Vector Search
                </h4>
                <div>
                    <button class="btn btn-light btn-sm me-2" onclick="toggleDocumentPanel()">
                        <i class="fas fa-file-alt me-1"></i>
                        Documents
                    </button>
                    <button class="btn btn-light btn-sm" onclick="showUploadModal()">
                        <i class="fas fa-upload me-1"></i>
                        Upload
                    </button>
                </div>
            </div>
            
            <div class="card-body p-0">
                <div class="row g-0">
                    <div class="col-md-8">
                        <div id="chatContainer" class="chat-container">
                            <div class="message bot-message">
                                <strong>Assistant:</strong> Hello! I'm powered by Qwen3:14b with vector search capabilities. I can chat with you and search through uploaded documents. How can I help you today?
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 border-start" id="documentPanel" style="display: none;">
                        <div class="p-3">
                            <h6 class="mb-3">
                                <i class="fas fa-search me-2"></i>
                                Document Search
                            </h6>
                            <div class="mb-3">
                                <input type="text" id="documentSearchInput" class="form-control form-control-sm"
                                       placeholder="Search documents...">
                                <button class="btn btn-primary btn-sm mt-2 w-100" onclick="searchDocuments()">
                                    <i class="fas fa-search me-1"></i>
                                    Search
                                </button>
                            </div>
                            <div id="documentResults" class="document-results">
                                <p class="text-muted small">Enter a search query to find relevant documents.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-circle-notch fa-spin me-2"></i>
                    Assistant is typing...
                </div>
                
                <div class="input-group">
                    <input type="text" id="messageInput" class="form-control message-input" 
                           placeholder="Type your message here..." maxlength="1000">
                    <button class="btn btn-primary send-button" type="button" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                
                <small class="text-muted mt-2 d-block">
                    Press Enter to send • Powered by Ollama
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>
                    Upload Document
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">Select Document</label>
                        <input type="file" class="form-control" id="fileInput" accept=".docx,.txt" required>
                        <div class="form-text">Supported formats: .docx, .txt</div>
                    </div>
                    <div id="uploadProgress" class="progress mb-3" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="uploadResult" class="alert" style="display: none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadDocument()">
                    <i class="fas fa-upload me-1"></i>
                    Upload
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- DOMPurify for HTML sanitization -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    <script src="~/js/chat.js"></script>
    <script src="~/js/documents.js"></script>
}
