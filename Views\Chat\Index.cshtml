@{
    ViewData["Title"] = "Chat";
}

<div class="row">
    <div class="col-12">
        <div class="status-indicator">
            @if (ViewBag.OllamaAvailable)
            {
                <span class="badge bg-success">
                    <i class="fas fa-circle me-1"></i>
                    Ollama Connected
                </span>
            }
            else
            {
                <span class="badge bg-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Ollama Disconnected
                </span>
            }
            <span id="connectionStatus" class="badge bg-secondary ms-2">
                <i class="fas fa-wifi me-1"></i>
                Connecting...
            </span>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    Chat with Qwen3:14b
                </h4>
            </div>
            
            <div class="card-body p-0">
                <div id="chatContainer" class="chat-container">
                    <div class="message bot-message">
                        <strong>Assistant:</strong> Hello! I'm powered by Qwen3:14b running on Ollama. How can I help you today?
                    </div>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-circle-notch fa-spin me-2"></i>
                    Assistant is typing...
                </div>
                
                <div class="input-group">
                    <input type="text" id="messageInput" class="form-control message-input" 
                           placeholder="Type your message here..." maxlength="1000">
                    <button class="btn btn-primary send-button" type="button" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                
                <small class="text-muted mt-2 d-block">
                    Press Enter to send • Powered by Ollama
                </small>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/chat.js"></script>
}
