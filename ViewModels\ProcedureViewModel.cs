using ChatbotApp.Models;

namespace ChatbotApp.ViewModels;

// ViewModel for the form
public class ProcedureViewModel
{
    public Procedure Procedure { get; set; } = new();
    public string FlowStepsJson { get; set; } = "[]";
    public string <PERSON><PERSON><PERSON><PERSON> { get; set; } = "[]";
    public string <PERSON><PERSON><PERSON><PERSON> { get; set; } = "[]";
    public string FormsJson { get; set; } = "[]";
    public string ReferencesText { get; set; } = string.Empty; // References as comma-separated text
}
