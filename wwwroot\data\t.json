{"Key": "63369f63-7771-4692-9ba4-3997d9d71e69", "Id": "t", "Title": "t", "Sector": "tt", "Department": "t", "Section": "t", "Description": "t", "Version": "t", "Objective": "t", "Scope": "t", "Policy": "t", "Responsibility": "t", "Definitions": "t", "CreatedDate": "0001-01-01T00:00:00", "UpdatedDate": "0001-01-01T00:00:00", "FlowSteps": [{"Step": 1, "Action": "t", "Responsible": "t", "Duration": "t"}], "Raci": [{"Task": "t", "Responsible": "t", "Accountable": "t"}], "Kpis": [{"Name": "t", "Type": "Duration", "Value": "t"}], "Forms": [{"Name": "t", "Code": "", "Retention": ""}], "References": ["t"]}