{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vs+04+m1kXh35DxL9GuAiNl2fdVJmtAQP+0zkFh2GzQ="], "CachedAssets": {"vs+04+m1kXh35DxL9GuAiNl2fdVJmtAQP+0zkFh2GzQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint=5g4imy9nsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m895dfzpr7", "Integrity": "+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "FileLength": 1350, "LastWriteTime": "2025-07-17T20:00:14.8852394+00:00"}}, "CachedCopyCandidates": {}}