{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vs+04+m1kXh35DxL9GuAiNl2fdVJmtAQP+0zkFh2GzQ=", "qpTtNYZztl9zGinjjTRT4N+zGvX2LqIsdkeTIa9ZWj0=", "D9T/PJx/+SqLnNZn8vURlWFw23Okpa9jrpr600322hw=", "9b7J74gM8dJEO0eGppf+VoTToq+GdEWkLW5BomqVyA4="], "CachedAssets": {"vs+04+m1kXh35DxL9GuAiNl2fdVJmtAQP+0zkFh2GzQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\agklrnpc3e-5g4imy9nsy.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint=5g4imy9nsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m895dfzpr7", "Integrity": "+ghVv6H1HVqogTYObcgddgvCI4N9E/hH64mSF55POKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "FileLength": 1350, "LastWriteTime": "2025-07-17T20:00:14.8852394+00:00"}, "qpTtNYZztl9zGinjjTRT4N+zGvX2LqIsdkeTIa9ZWj0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\cvyrwjbpcf-awmycvggfz.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint=awmycvggfz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5v3pw8xpx3", "Integrity": "0W+dRyNR39YLZkGHLJ03Ipmv0gvZkKBd2osKZ8PtD6I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "FileLength": 2466, "LastWriteTime": "2025-07-17T21:13:27.344604+00:00"}, "D9T/PJx/+SqLnNZn8vURlWFw23Okpa9jrpr600322hw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\c56qs9up7j-wi72cewi0b.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/FIN-03#[.{fingerprint=wi72cewi0b}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu6m908vdq", "Integrity": "yUry0guujduN3BKimE/+CQg3fQ0xxCOKSQ2OoimURpw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "FileLength": 1138, "LastWriteTime": "2025-07-18T18:05:23.0727929+00:00"}, "9b7J74gM8dJEO0eGppf+VoTToq+GdEWkLW5BomqVyA4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\vpkb7ye57x-if7imjr4iq.gz", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/HR-07#[.{fingerprint=if7imjr4iq}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5vx2ncb4h9", "Integrity": "DxG99TXc5BWIeFh7xhOd3j7UstBG1kMO5STaF4fnaM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "FileLength": 1279, "LastWriteTime": "2025-07-18T18:05:23.0743417+00:00"}}, "CachedCopyCandidates": {}}