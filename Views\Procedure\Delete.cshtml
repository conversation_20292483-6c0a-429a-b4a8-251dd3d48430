@model ChatbotApp.Models.ProcedureVectorItem

@{
    ViewData["Title"] = "Delete Procedure";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Delete Procedure
                </h2>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to List
                </a>
            </div>

            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Warning!
                </h4>
                <p>Are you sure you want to delete this procedure? This action cannot be undone.</p>
                <hr>
                <p class="mb-0">The JSON file will be permanently removed from the system.</p>
            </div>

            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Procedure to be Deleted
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">ID:</dt>
                                <dd class="col-sm-8"><span class="badge bg-primary fs-6">@Model.Id</span></dd>
                                
                                <dt class="col-sm-4">Title:</dt>
                                <dd class="col-sm-8"><strong>@Model.Payload.Title</strong></dd>
                                
                                <dt class="col-sm-4">Version:</dt>
                                <dd class="col-sm-8"><span class="badge bg-info fs-6">@Model.Payload.Version</span></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Sector:</dt>
                                <dd class="col-sm-8">@Model.Payload.Sector</dd>
                                
                                <dt class="col-sm-4">Department:</dt>
                                <dd class="col-sm-8">@Model.Payload.Department</dd>
                                
                                <dt class="col-sm-4">Section:</dt>
                                <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.Payload.Section) ? "N/A" : Model.Payload.Section)</dd>
                            </dl>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Payload.Objective))
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">Objective:</h6>
                            <p class="text-muted">@Model.Payload.Objective</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Description))
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">Description:</h6>
                            <p class="text-muted">@Model.Payload.Description</p>
                        </div>
                    }

                    @if (Model.Payload.FlowSteps.Any())
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">Flow Steps:</h6>
                            <p class="text-muted">@Model.Payload.FlowSteps.Count step(s) defined</p>
                        </div>
                    }

                    @if (Model.Payload.Raci.Any())
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">RACI Matrix:</h6>
                            <p class="text-muted">@Model.Payload.Raci.Count task(s) defined</p>
                        </div>
                    }

                    @if (Model.Payload.Kpis.Any())
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">KPIs:</h6>
                            <p class="text-muted">@Model.Payload.Kpis.Count KPI(s) defined</p>
                        </div>
                    }

                    @if (Model.Payload.Forms.Any())
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">Forms:</h6>
                            <p class="text-muted">@Model.Payload.Forms.Count form(s) defined</p>
                        </div>
                    }

                    @if (Model.Payload.References.Any())
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">References:</h6>
                            <p class="text-muted">@Model.Payload.References.Count reference(s) defined</p>
                        </div>
                    }
                </div>
            </div>

            <div class="mt-4 mb-4">
                <form asp-action="Delete" method="post" class="d-inline">
                    <button type="submit" class="btn btn-danger btn-lg me-3" 
                            onclick="return confirm('Are you absolutely sure you want to delete this procedure? This action cannot be undone.')">
                        <i class="fas fa-trash me-2"></i>
                        Yes, Delete Procedure
                    </button>
                </form>
                
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-lg me-3">
                    <i class="fas fa-eye me-2"></i>
                    View Details
                </a>
                
                <a asp-action="Index" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>
                    Cancel
                </a>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Additional confirmation for delete button
        document.querySelector('form button[type="submit"]').addEventListener('click', function(e) {
            if (!confirm('This will permanently delete the procedure "@Model.Id". Are you sure?')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
}
