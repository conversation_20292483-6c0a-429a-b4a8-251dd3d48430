using Microsoft.AspNetCore.Mvc;
using ChatbotApp.Models;
using ChatbotApp.Services;
using System.Text.Json;

namespace ChatbotApp.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DocumentController : ControllerBase
{
    private readonly IDocumentProcessingService _documentService;
    private readonly IVectorService _vectorService;
    private readonly ILogger<DocumentController> _logger;

    public DocumentController(
        IDocumentProcessingService documentService,
        IVectorService vectorService,
        ILogger<DocumentController> logger)
    {
        _documentService = documentService;
        _vectorService = vectorService;
        _logger = logger;
    }

    [HttpPost("upload")]
    public async Task<IActionResult> UploadDocument(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "No file uploaded" });
            }

            // Validate file type
            var allowedExtensions = new[] { ".docx", ".txt" };
            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            
            if (!allowedExtensions.Contains(fileExtension))
            {
                return BadRequest(new { message = "Only .docx and .txt files are supported" });
            }

            // Process the document
            using var stream = file.OpenReadStream();
            var result = await _documentService.ProcessDocumentAsync(stream, file.FileName);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = result.Message,
                    document = new
                    {
                        id = result.DocumentInfo?.Id,
                        fileName = result.DocumentInfo?.FileName,
                        title = result.DocumentInfo?.Title,
                        summary = result.DocumentInfo?.Summary,
                        keywords = result.DocumentInfo?.Keywords,
                        metadata = result.DocumentInfo?.Metadata
                    }
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.Message,
                    errors = result.Errors
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document: {FileName}", file?.FileName);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpPost("search")]
    public async Task<IActionResult> SearchDocuments([FromBody] DocumentSearchRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Query))
            {
                return BadRequest(new { message = "Query is required" });
            }

            var results = await _vectorService.SearchAsync(request.Query, request.TopK, request.MinScore);

            return Ok(new
            {
                success = true,
                query = request.Query,
                results = results.Select(r => new
                {
                    documentId = r.DocumentId,
                    fileName = r.FileName,
                    title = r.Title,
                    content = r.Content,
                    score = r.Score,
                    metadata = r.Metadata
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents with query: {Query}", request.Query);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpGet("list")]
    public async Task<IActionResult> GetDocuments()
    {
        try
        {
            var documents = await _documentService.GetAllDocumentsAsync();

            return Ok(new
            {
                success = true,
                documents = documents.Select(d => new
                {
                    id = d.Id,
                    fileName = d.FileName,
                    title = d.Title,
                    summary = d.Summary,
                    keywords = d.Keywords,
                    createdDate = d.CreatedDate,
                    modifiedDate = d.ModifiedDate,
                    fileSize = d.FileSize,
                    author = d.Author,
                    metadata = d.Metadata
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents");
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetDocument(string id)
    {
        try
        {
            var document = await _documentService.GetDocumentByIdAsync(id);

            if (document == null)
            {
                return NotFound(new { message = "Document not found" });
            }

            return Ok(new
            {
                success = true,
                document = new
                {
                    id = document.Id,
                    fileName = document.FileName,
                    title = document.Title,
                    content = document.Content,
                    summary = document.Summary,
                    keywords = document.Keywords,
                    createdDate = document.CreatedDate,
                    modifiedDate = document.ModifiedDate,
                    fileSize = document.FileSize,
                    author = document.Author,
                    metadata = document.Metadata
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document: {DocumentId}", id);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteDocument(string id)
    {
        try
        {
            var success = await _documentService.DeleteDocumentAsync(id);

            if (success)
            {
                return Ok(new { success = true, message = "Document deleted successfully" });
            }
            else
            {
                return NotFound(new { message = "Document not found" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", id);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpPost("process-existing")]
    public async Task<IActionResult> ProcessExistingFiles()
    {
        try
        {
            var filesDirectory = Path.Combine("wwwroot", "files");
            if (!Directory.Exists(filesDirectory))
            {
                return BadRequest(new { message = "Files directory not found" });
            }

            var files = Directory.GetFiles(filesDirectory, "*.*", SearchOption.TopDirectoryOnly)
                .Where(f => Path.GetExtension(f).ToLower() is ".docx" or ".txt")
                .ToList();

            var results = new List<object>();

            foreach (var filePath in files)
            {
                try
                {
                    var result = await _documentService.ProcessDocumentAsync(filePath);
                    results.Add(new
                    {
                        fileName = Path.GetFileName(filePath),
                        success = result.Success,
                        message = result.Message,
                        documentId = result.DocumentInfo?.Id
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing file: {FilePath}", filePath);
                    results.Add(new
                    {
                        fileName = Path.GetFileName(filePath),
                        success = false,
                        message = ex.Message
                    });
                }
            }

            return Ok(new
            {
                success = true,
                message = $"Processed {files.Count} files",
                results = results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing existing files");
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetStatus()
    {
        try
        {
            var vectorServiceAvailable = await _vectorService.IsAvailableAsync();
            var documentsCount = (await _documentService.GetAllDocumentsAsync()).Count;

            return Ok(new
            {
                success = true,
                status = new
                {
                    vectorServiceAvailable = vectorServiceAvailable,
                    documentsCount = documentsCount,
                    qdrantEndpoint = "http://localhost:6333",
                    supportedFormats = new[] { ".docx", ".txt" }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting status");
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }
}
