namespace ChatbotApp.Models;

public class ProcedureVectorItem
{
    public string Id { get; set; } = string.Empty;                     // مثال: "HR-07"
    public float[] Vector { get; set; } = Array.Empty<float>();        // سيتم ملؤه لاحقاً من نموذج embeddings
    public ProcedurePayload Payload { get; set; } = new();             // بيانات الإجراء
}

public class ProcedurePayload
{
    public string Sector { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Section { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Objective { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
    public string Policy { get; set; } = string.Empty;
    public string Responsibility { get; set; } = string.Empty;
    public string Definitions { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;

    public List<ProcedureStep> FlowSteps { get; set; } = new();
    public List<ProcedureRaci> Raci { get; set; } = new();
    public List<ProcedureKpi> Kpis { get; set; } = new();
    public List<ProcedureForm> Forms { get; set; } = new();
    public List<string> References { get; set; } = new();
}

public class ProcedureStep
{
    public int Step { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Responsible { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
}

public class ProcedureRaci
{
    public string Task { get; set; } = string.Empty;
    public string Responsible { get; set; } = string.Empty;
    public string Accountable { get; set; } = string.Empty;
}

public class ProcedureKpi
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class ProcedureForm
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Retention { get; set; } = string.Empty;
}

// ViewModel for the form
public class ProcedureViewModel
{
    public ProcedureVectorItem Procedure { get; set; } = new();
    public string FlowStepsJson { get; set; } = "[]";
    public string RaciJson { get; set; } = "[]";
    public string KpisJson { get; set; } = "[]";
    public string FormsJson { get; set; } = "[]";
    public string ReferencesText { get; set; } = string.Empty; // References as comma-separated text
}
