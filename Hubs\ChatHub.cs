using Microsoft.AspNetCore.SignalR;
using ChatbotApp.Models;
using AspNetCoreGeneratedDocument;
using ChatbotApp.Services.Interfaces;

namespace ChatbotApp.Hubs;

public class ChatHub : Hub
{
    private readonly IOllamaService _ollamaService;
    //private readonly IVectorService _vectorService;
    // private readonly IPrcedureVectorService _prcedureVectorService; // Temporarily disabled

    private readonly ILogger<ChatHub> _logger;

    public ChatHub(IOllamaService ollamaService, ILogger<ChatHub> logger)
    {
        _ollamaService = ollamaService;
        // _prcedureVectorService = prcedureVectorService; // Temporarily disabled
        _logger = logger;
    }

    public async Task SendMessage(string message)
    {
        try
        {
            _logger.LogInformation("Received message from client: {Message}", message);

            // Check if Ollama is available
            if (!await _ollamaService.IsAvailableAsync())
            {
                await Clients.Caller.SendAsync("ReceiveError", "Ollama service is not available. Please make sure <PERSON>llama is running on localhost:11434");
                return;
            }

            // Send user message to all clients
            await Clients.All.SendAsync("ReceiveMessage", "User", message);


            // Check if this is a document search query
            var enhancedPrompt = await EnhancePromptWithDocuments(message);

            // Start streaming response from Ollama
            await Clients.Caller.SendAsync("StartBotResponse");

            var responseStream = _ollamaService.GenerateStreamAsync(enhancedPrompt, Context.ConnectionAborted);

            await foreach (var chunk in responseStream)
            {
                if (Context.ConnectionAborted.IsCancellationRequested)
                    break;

                await Clients.Caller.SendAsync("ReceiveChunk", chunk);
            }

            // Signal end of response
            await Clients.Caller.SendAsync("EndBotResponse");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message: {Message}", message);
            await Clients.Caller.SendAsync("ReceiveError", $"An error occurred: {ex.Message}");
        }
    }

    public async Task SearchDocuments(string query)
    {
        try
        {
            _logger.LogInformation("Searching documents for query: {Query}", query);

            // TODO: Integrate with ProcedureVectorService
            var searchResults = new List<object>(); // Temporary placeholder

            await Clients.Caller.SendAsync("DocumentSearchResults", new
            {
                query = query,
                results = searchResults
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents: {Query}", query);
            await Clients.Caller.SendAsync("ReceiveError", $"Error searching documents: {ex.Message}");
        }
    }

    private async Task<string> EnhancePromptWithDocuments(string userMessage)
    {
        try
        {
            // Check if the message seems like it's asking about documents
            var documentKeywords = new[] { "document", "file", "paper", "report", "what does", "tell me about", "explain", "summarize" };
            var isDocumentQuery = documentKeywords.Any(keyword =>
                userMessage.ToLower().Contains(keyword.ToLower()));

            if (!isDocumentQuery)
                return userMessage;

            // TODO: Integrate with ProcedureVectorService
            // For now, return the original message
            return userMessage;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enhancing prompt with documents, using original message");
            return userMessage;
        }
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }
}
