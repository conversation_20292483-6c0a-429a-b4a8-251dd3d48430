using Microsoft.AspNetCore.SignalR;
using ChatbotApp.Services;

namespace ChatbotApp.Hubs;

public class ChatHub : Hub
{
    private readonly IOllamaService _ollamaService;
    private readonly ILogger<ChatHub> _logger;

    public ChatHub(IOllamaService ollamaService, ILogger<ChatHub> logger)
    {
        _ollamaService = ollamaService;
        _logger = logger;
    }

    public async Task SendMessage(string message)
    {
        try
        {
            _logger.LogInformation("Received message from client: {Message}", message);

            // Check if Ollama is available
            if (!await _ollamaService.IsAvailableAsync())
            {
                await Clients.Caller.SendAsync("ReceiveError", "Ollama service is not available. Please make sure Ollama is running on localhost:11434");
                return;
            }

            // Send user message to all clients
            await Clients.All.SendAsync("ReceiveMessage", "User", message);

            // Start streaming response from Ollama
            await Clients.Caller.SendAsync("StartBotResponse");

            var responseStream = _ollamaService.GenerateStreamAsync(message, Context.ConnectionAborted);

            await foreach (var chunk in responseStream)
            {
                if (Context.ConnectionAborted.IsCancellationRequested)
                    break;

                await Clients.Caller.SendAsync("ReceiveChunk", chunk);
            }

            // Signal end of response
            await Clients.Caller.SendAsync("EndBotResponse");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message: {Message}", message);
            await Clients.Caller.SendAsync("ReceiveError", $"An error occurred: {ex.Message}");
        }
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }
}
