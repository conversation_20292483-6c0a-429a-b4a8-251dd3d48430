using Microsoft.AspNetCore.SignalR;
using ChatbotApp.Models;
using AspNetCoreGeneratedDocument;
using ChatbotApp.Services.Interfaces;

namespace ChatbotApp.Hubs;

public class ChatHub : Hub
{
    private readonly IOllamaService _ollamaService;
    //private readonly IVectorService _vectorService;
    private readonly IPrcedureVectorService _prcedureVectorService;

    private readonly ILogger<ChatHub> _logger;

    public ChatHub(IOllamaService ollamaService, IPrcedureVectorService prcedureVectorService, ILogger<ChatHub> logger)
    {
        _ollamaService = ollamaService;
        _prcedureVectorService = prcedureVectorService;
        _logger = logger;
    }

    public async Task SendMessage(string message)
    {
        try
        {
            _logger.LogInformation("Received message from client: {Message}", message);

            // Check if Ollama is available
            if (!await _ollamaService.IsAvailableAsync())
            {
                await Clients.Caller.SendAsync("ReceiveError", "Ollama service is not available. Please make sure Ollama is running on localhost:11434");
                return;
            }

            // Send user message to all clients
            await Clients.All.SendAsync("ReceiveMessage", "User", message);


            // Check if this is a document search query
            var enhancedPrompt = await EnhancePromptWithDocuments(message);

            // Start streaming response from Ollama
            await Clients.Caller.SendAsync("StartBotResponse");

            var responseStream = _ollamaService.GenerateStreamAsync(enhancedPrompt, Context.ConnectionAborted);

            await foreach (var chunk in responseStream)
            {
                if (Context.ConnectionAborted.IsCancellationRequested)
                    break;

                await Clients.Caller.SendAsync("ReceiveChunk", chunk);
            }

            // Signal end of response
            await Clients.Caller.SendAsync("EndBotResponse");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message: {Message}", message);
            await Clients.Caller.SendAsync("ReceiveError", $"An error occurred: {ex.Message}");
        }
    }

    public async Task SearchDocuments(string query)
    {
        try
        {
            _logger.LogInformation("Searching documents for query: {Query}", query);

            var searchResults = await _prcedureVectorService.SearchAsync(query, topK: 5, minScore: 0.3f);

            await Clients.Caller.SendAsync("DocumentSearchResults", new
            {
                query = query,
                results = searchResults.Select(r => new
                {

                    documentId = r.DocumentId,
                    fileName = r.FileName,
                    title = r.Title,
                    content = r.Content.Length > 200 ? r.Content.Substring(0, 200) + "..." : r.Content,
                    score = r.Score,
                    metadata = r.Metadata
                }).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents: {Query}", query);
            await Clients.Caller.SendAsync("ReceiveError", $"Error searching documents: {ex.Message}");
        }
    }

    private async Task<string> EnhancePromptWithDocuments(string userMessage)
    {
        try
        {
            // Check if the message seems like it's asking about documents
            var documentKeywords = new[] { "document", "file", "paper", "report", "what does", "tell me about", "explain", "summarize" };
            var isDocumentQuery = documentKeywords.Any(keyword =>
                userMessage.ToLower().Contains(keyword.ToLower()));

            if (!isDocumentQuery)
                return userMessage;

            // Search for relevant documents
            var searchResults = await _prcedureVectorService.SearchAsync(userMessage, topK: 3, minScore: 0.3f);

            if (searchResults.Count == 0)
                return userMessage;

            // Enhance the prompt with document context
            var contextBuilder = new System.Text.StringBuilder();
            contextBuilder.AppendLine("Based on the following document excerpts, please answer the user's question:");
            contextBuilder.AppendLine();

            foreach (var result in searchResults)
            {
                contextBuilder.AppendLine($"From document '{result.FileName}' (Score: {result.Score:F2}):");
                contextBuilder.AppendLine(result.Content);
                contextBuilder.AppendLine();
            }

            contextBuilder.AppendLine($"User Question: {userMessage}");
            contextBuilder.AppendLine();
            contextBuilder.AppendLine("Please provide a comprehensive answer based on the document excerpts above. If the documents don't contain relevant information, please say so.");

            return contextBuilder.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enhancing prompt with documents, using original message");
            return userMessage;
        }
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }
}
