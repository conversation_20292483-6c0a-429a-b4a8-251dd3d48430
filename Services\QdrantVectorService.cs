using ChatbotApp.Models;
using ChatbotApp.Services.Interfaces;
using Microsoft.Extensions.AI;
using Microsoft.SemanticKernel;
using System.Text;
using System.Text.Json;

namespace ChatbotApp.Services;

public class QdrantVectorService : IVectorService
{
    private readonly ILogger<QdrantVectorService> _logger;
    private readonly Kernel _kernel;
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly string _collectionName = "documents";
    private readonly string _qdrantEndpoint = "http://localhost:6333";
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;

    // In-memory storage for demo purposes (replace with actual Qdrant client)
    private readonly List<DocumentChunk> _vectorStore = new();

    public QdrantVectorService(ILogger<QdrantVectorService> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();

        // Create kernel with embedding service (using Ollama for embeddings)
        var builder = Kernel.CreateBuilder();
        builder.AddOllamaEmbeddingGenerator(
            modelId: "nomic-embed-text:latest", // Use the embedding model you have
            endpoint: new Uri("http://localhost:11434"));

        _kernel = builder.Build();
        _embeddingService = _kernel.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Check if Qdrant is available
            var response = await _httpClient.GetAsync($"{_qdrantEndpoint}/collections");

            if (response.IsSuccessStatusCode)
            {
                // Create collection if it doesn't exist
                await CreateCollectionIfNotExistsAsync();
                _isInitialized = true;
                _logger.LogInformation("Qdrant vector service initialized successfully");
            }
            else
            {
                _logger.LogWarning("Qdrant service not available, using in-memory storage");
                _isInitialized = true; // Use in-memory storage as fallback
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Qdrant service not available, using in-memory storage");
            _isInitialized = true; // Use in-memory storage as fallback
        }
    }

    private async Task CreateCollectionIfNotExistsAsync()
    {
        try
        {
            // Check if collection exists
            var checkResponse = await _httpClient.GetAsync($"{_qdrantEndpoint}/collections/{_collectionName}");

            if (checkResponse.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Create collection
                var createRequest = new
                {
                    vectors = new
                    {
                        size = 768, // nomic-embed-text embedding size (updated to match actual size)
                        distance = "Cosine"
                    }
                };

                var json = JsonSerializer.Serialize(createRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var createResponse = await _httpClient.PutAsync($"{_qdrantEndpoint}/collections/{_collectionName}", content);

                if (createResponse.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Created Qdrant collection: {CollectionName}", _collectionName);
                }
                else
                {
                    _logger.LogWarning("Failed to create Qdrant collection: {CollectionName}", _collectionName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Qdrant collection");
        }
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            if (!_isInitialized)
                await InitializeAsync();

            return _isInitialized;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Vector service is not available");
            return false;
        }
    }

    public async Task<bool> IndexDocumentAsync(DocumentInfo document)
    {
        try
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
                if (!_isInitialized)
                    return false;
            }

            // Split document into chunks for better vector search
            var chunks = SplitIntoChunks(document.Content, 500); // 500 character chunks

            for (int i = 0; i < chunks.Count; i++)
            {
                var chunk = chunks[i];
                if (string.IsNullOrWhiteSpace(chunk))
                    continue;

                // Create metadata for the chunk
                var metadata = new Dictionary<string, object>
                {
                    ["documentId"] = document.Id,
                    ["fileName"] = document.FileName,
                    ["title"] = document.Title,
                    ["chunkIndex"] = i,
                    ["author"] = document.Author,
                    ["createdDate"] = document.CreatedDate.ToString("O"),
                    ["keywords"] = JsonSerializer.Serialize(document.Keywords),
                    ["summary"] = document.Summary
                };

                // Generate embedding for the chunk
                _logger.LogInformation("Generating embedding for chunk {ChunkIndex} of document {FileName}", i, document.FileName);
                var embedding = await GenerateEmbeddingAsync(chunk);
                _logger.LogInformation("Generated embedding with {VectorSize} dimensions for chunk {ChunkIndex}", embedding.Length, i);

                // Store in Qdrant (use UUID for point ID)
                var pointId = Guid.NewGuid().ToString();

                // Add chunk-specific metadata
                var chunkMetadata = new Dictionary<string, object>(metadata)
                {
                    ["pointId"] = pointId,
                    ["originalChunkId"] = $"{document.Id}_chunk_{i}"
                };

                _logger.LogInformation("Storing point {PointId} in Qdrant for chunk {ChunkIndex}", pointId, i);
                await StoreInQdrantAsync(pointId, embedding, chunk, chunkMetadata);

                // Also store in memory as backup
                var documentChunk = new DocumentChunk
                {
                    Id = pointId,
                    DocumentId = document.Id,
                    Content = chunk,
                    ChunkIndex = i,
                    Vector = embedding,
                    Metadata = chunkMetadata
                };

                _vectorStore.Add(documentChunk);
            }

            _logger.LogInformation("Successfully indexed document: {FileName} with {ChunkCount} chunks",
                document.FileName, chunks.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing document: {FileName}", document.FileName);
            return false;
        }
    }

    public async Task<List<DocumentSearchResult>> SearchAsync(string query, int topK = 5, float minScore = 0.7f)
    {
        try
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
                if (!_isInitialized)
                    return new List<DocumentSearchResult>();
            }

            // Generate embedding for the query
            var queryEmbedding = await GenerateEmbeddingAsync(query);

            // Try Qdrant first
            var qdrantResults = await SearchInQdrantAsync(queryEmbedding, topK, minScore);

            if (qdrantResults.Count > 0)
            {
                _logger.LogInformation("Found {ResultCount} results in Qdrant for query: {Query}", qdrantResults.Count, query);
                return qdrantResults;
            }

            // Fall back to in-memory search
            var results = new List<DocumentSearchResult>();

            foreach (var chunk in _vectorStore)
            {
                var similarity = CalculateCosineSimilarity(queryEmbedding, chunk.Vector);

                if (similarity >= minScore)
                {
                    var result = new DocumentSearchResult
                    {
                        DocumentId = chunk.Metadata.GetValueOrDefault("documentId")?.ToString() ?? "",
                        FileName = chunk.Metadata.GetValueOrDefault("fileName")?.ToString() ?? "",
                        Title = chunk.Metadata.GetValueOrDefault("title")?.ToString() ?? "",
                        Content = chunk.Content,
                        Score = similarity,
                        Metadata = chunk.Metadata
                    };

                    results.Add(result);
                }
            }

            // Sort by score and take top K
            results = results.OrderByDescending(r => r.Score).Take(topK).ToList();

            _logger.LogInformation("Found {ResultCount} results in memory for query: {Query}", results.Count, query);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents with query: {Query}", query);
            return new List<DocumentSearchResult>();
        }
    }

    public async Task<bool> DeleteDocumentAsync(string documentId)
    {
        try
        {
            if (!_isInitialized)
                return false;

            // Remove all chunks for the document
            var chunksToRemove = _vectorStore.Where(c => c.DocumentId == documentId).ToList();
            foreach (var chunk in chunksToRemove)
            {
                _vectorStore.Remove(chunk);
            }

            _logger.LogInformation("Deleted {ChunkCount} chunks for document: {DocumentId}",
                chunksToRemove.Count, documentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", documentId);
            return false;
        }
    }

    public async Task<float[]> GenerateEmbeddingAsync(string text)
    {
        try
        {
            var embedding = await _embeddingService.GenerateAsync(text);
            return embedding.Vector.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding for text");
            throw;
        }
    }

    private float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
            return 0f;

        float dotProduct = 0f;
        float magnitude1 = 0f;
        float magnitude2 = 0f;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = (float)Math.Sqrt(magnitude1);
        magnitude2 = (float)Math.Sqrt(magnitude2);

        if (magnitude1 == 0f || magnitude2 == 0f)
            return 0f;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private List<string> SplitIntoChunks(string text, int chunkSize = 500, int overlap = 50)
    {
        var chunks = new List<string>();
        
        if (string.IsNullOrWhiteSpace(text))
            return chunks;

        var sentences = text.Split('.', StringSplitOptions.RemoveEmptyEntries);
        var currentChunk = new List<string>();
        var currentLength = 0;

        foreach (var sentence in sentences)
        {
            var trimmedSentence = sentence.Trim();
            if (string.IsNullOrEmpty(trimmedSentence))
                continue;

            var sentenceWithPeriod = trimmedSentence + ".";
            
            if (currentLength + sentenceWithPeriod.Length > chunkSize && currentChunk.Count > 0)
            {
                // Add current chunk
                chunks.Add(string.Join(" ", currentChunk));
                
                // Start new chunk with overlap
                if (overlap > 0 && currentChunk.Count > 1)
                {
                    var overlapSentences = currentChunk.TakeLast(Math.Min(2, currentChunk.Count - 1)).ToList();
                    currentChunk = overlapSentences;
                    currentLength = string.Join(" ", overlapSentences).Length;
                }
                else
                {
                    currentChunk.Clear();
                    currentLength = 0;
                }
            }

            currentChunk.Add(sentenceWithPeriod);
            currentLength += sentenceWithPeriod.Length + 1; // +1 for space
        }

        // Add the last chunk if it has content
        if (currentChunk.Count > 0)
        {
            chunks.Add(string.Join(" ", currentChunk));
        }

        return chunks;
    }

    private async Task StoreInQdrantAsync(string id, float[] vector, string content, Dictionary<string, object> metadata)
    {
        try
        {
            var point = new
            {
                id = id,
                vector = vector,
                payload = new Dictionary<string, object>(metadata)
                {
                    ["content"] = content
                }
            };

            var request = new
            {
                points = new[] { point }
            };

            var json = JsonSerializer.Serialize(request);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync($"{_qdrantEndpoint}/collections/{_collectionName}/points", httpContent);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully stored point {PointId} in Qdrant", id);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to store point in Qdrant: {Error}", errorContent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing point in Qdrant");
        }
    }

    private async Task<List<DocumentSearchResult>> SearchInQdrantAsync(float[] queryVector, int topK, float minScore)
    {
        try
        {
            var searchRequest = new
            {
                vector = queryVector,
                limit = topK,
                score_threshold = minScore,
                with_payload = true
            };

            var json = JsonSerializer.Serialize(searchRequest);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_qdrantEndpoint}/collections/{_collectionName}/points/search", httpContent);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Qdrant search failed, falling back to in-memory search");
                return new List<DocumentSearchResult>();
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var searchResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var results = new List<DocumentSearchResult>();

            if (searchResponse.TryGetProperty("result", out var resultArray))
            {
                foreach (var item in resultArray.EnumerateArray())
                {
                    if (item.TryGetProperty("payload", out var payload) &&
                        item.TryGetProperty("score", out var scoreElement))
                    {
                        var result = new DocumentSearchResult
                        {
                            DocumentId = payload.TryGetProperty("documentId", out var docId) ? docId.GetString() ?? "" : "",
                            FileName = payload.TryGetProperty("fileName", out var fileName) ? fileName.GetString() ?? "" : "",
                            Title = payload.TryGetProperty("title", out var title) ? title.GetString() ?? "" : "",
                            Content = payload.TryGetProperty("content", out var content) ? content.GetString() ?? "" : "",
                            Score = scoreElement.GetSingle(),
                            Metadata = new Dictionary<string, object>()
                        };

                        // Extract metadata
                        foreach (var prop in payload.EnumerateObject())
                        {
                            if (prop.Name != "content")
                            {
                                result.Metadata[prop.Name] = prop.Value.ToString();
                            }
                        }

                        results.Add(result);
                    }
                }
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching in Qdrant");
            return new List<DocumentSearchResult>();
        }
    }
}
