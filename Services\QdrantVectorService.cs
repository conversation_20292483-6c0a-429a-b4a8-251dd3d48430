using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Qdrant;
using ChatbotApp.Models;
using System.Text.Json;
using Microsoft.Extensions.AI;

namespace ChatbotApp.Services;

public class QdrantVectorService : IVectorService
{
    private readonly ILogger<QdrantVectorService> _logger;
    private readonly Kernel _kernel;
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly string _collectionName = "documents";
    private readonly string _qdrantEndpoint = "http://localhost:6333";
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;

    // In-memory storage for demo purposes (replace with actual Qdrant client)
    private readonly List<DocumentChunk> _vectorStore = new();

    public QdrantVectorService(ILogger<QdrantVectorService> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();

        // Create kernel with embedding service (using Ollama for embeddings)
        var builder = Kernel.CreateBuilder();
        builder.AddOllamaEmbeddingGenerator(
            modelId: "nomic-embed-text:latest", // Use the embedding model you have
            endpoint: new Uri("http://localhost:11434"));

        _kernel = builder.Build();
        _embeddingService = _kernel.GetRequiredService<IEmbeddingGenerator<string, Embedding<float>>>();
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Try to connect to Qdrant (simplified check)
            var response = await _httpClient.GetAsync($"{_qdrantEndpoint}/collections");
            _isInitialized = response.IsSuccessStatusCode;

            if (_isInitialized)
            {
                _logger.LogInformation("Qdrant vector service initialized successfully");
            }
            else
            {
                _logger.LogWarning("Qdrant service not available, using in-memory storage");
                _isInitialized = true; // Use in-memory storage as fallback
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Qdrant service not available, using in-memory storage");
            _isInitialized = true; // Use in-memory storage as fallback
        }
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            if (!_isInitialized)
                await InitializeAsync();

            return _isInitialized;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Vector service is not available");
            return false;
        }
    }

    public async Task<bool> IndexDocumentAsync(DocumentInfo document)
    {
        try
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
                if (!_isInitialized)
                    return false;
            }

            // Split document into chunks for better vector search
            var chunks = SplitIntoChunks(document.Content, 500); // 500 character chunks

            for (int i = 0; i < chunks.Count; i++)
            {
                var chunk = chunks[i];
                if (string.IsNullOrWhiteSpace(chunk))
                    continue;

                // Create metadata for the chunk
                var metadata = new Dictionary<string, object>
                {
                    ["documentId"] = document.Id,
                    ["fileName"] = document.FileName,
                    ["title"] = document.Title,
                    ["chunkIndex"] = i,
                    ["author"] = document.Author,
                    ["createdDate"] = document.CreatedDate.ToString("O"),
                    ["keywords"] = JsonSerializer.Serialize(document.Keywords),
                    ["summary"] = document.Summary
                };

                // Generate embedding for the chunk
                var embedding = await GenerateEmbeddingAsync(chunk);

                // Store in memory (replace with actual Qdrant storage)
                var documentChunk = new DocumentChunk
                {
                    Id = $"{document.Id}_chunk_{i}",
                    DocumentId = document.Id,
                    Content = chunk,
                    ChunkIndex = i,
                    Vector = embedding,
                    Metadata = metadata
                };

                _vectorStore.Add(documentChunk);
            }

            _logger.LogInformation("Successfully indexed document: {FileName} with {ChunkCount} chunks",
                document.FileName, chunks.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing document: {FileName}", document.FileName);
            return false;
        }
    }

    public async Task<List<DocumentSearchResult>> SearchAsync(string query, int topK = 5, float minScore = 0.7f)
    {
        try
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
                if (!_isInitialized)
                    return new List<DocumentSearchResult>();
            }

            // Generate embedding for the query
            var queryEmbedding = await GenerateEmbeddingAsync(query);

            // Search in memory store (replace with actual Qdrant search)
            var results = new List<DocumentSearchResult>();

            foreach (var chunk in _vectorStore)
            {
                var similarity = CalculateCosineSimilarity(queryEmbedding, chunk.Vector);

                if (similarity >= minScore)
                {
                    var result = new DocumentSearchResult
                    {
                        DocumentId = chunk.Metadata.GetValueOrDefault("documentId")?.ToString() ?? "",
                        FileName = chunk.Metadata.GetValueOrDefault("fileName")?.ToString() ?? "",
                        Title = chunk.Metadata.GetValueOrDefault("title")?.ToString() ?? "",
                        Content = chunk.Content,
                        Score = similarity,
                        Metadata = chunk.Metadata
                    };

                    results.Add(result);
                }
            }

            // Sort by score and take top K
            results = results.OrderByDescending(r => r.Score).Take(topK).ToList();

            _logger.LogInformation("Found {ResultCount} results for query: {Query}", results.Count, query);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents with query: {Query}", query);
            return new List<DocumentSearchResult>();
        }
    }

    public async Task<bool> DeleteDocumentAsync(string documentId)
    {
        try
        {
            if (!_isInitialized)
                return false;

            // Remove all chunks for the document
            var chunksToRemove = _vectorStore.Where(c => c.DocumentId == documentId).ToList();
            foreach (var chunk in chunksToRemove)
            {
                _vectorStore.Remove(chunk);
            }

            _logger.LogInformation("Deleted {ChunkCount} chunks for document: {DocumentId}",
                chunksToRemove.Count, documentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", documentId);
            return false;
        }
    }

    public async Task<float[]> GenerateEmbeddingAsync(string text)
    {
        try
        {
            var embedding = await _embeddingService.GenerateAsync(text);
            return embedding.Vector.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding for text");
            throw;
        }
    }

    private float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
            return 0f;

        float dotProduct = 0f;
        float magnitude1 = 0f;
        float magnitude2 = 0f;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = (float)Math.Sqrt(magnitude1);
        magnitude2 = (float)Math.Sqrt(magnitude2);

        if (magnitude1 == 0f || magnitude2 == 0f)
            return 0f;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private List<string> SplitIntoChunks(string text, int chunkSize = 500, int overlap = 50)
    {
        var chunks = new List<string>();
        
        if (string.IsNullOrWhiteSpace(text))
            return chunks;

        var sentences = text.Split('.', StringSplitOptions.RemoveEmptyEntries);
        var currentChunk = new List<string>();
        var currentLength = 0;

        foreach (var sentence in sentences)
        {
            var trimmedSentence = sentence.Trim();
            if (string.IsNullOrEmpty(trimmedSentence))
                continue;

            var sentenceWithPeriod = trimmedSentence + ".";
            
            if (currentLength + sentenceWithPeriod.Length > chunkSize && currentChunk.Count > 0)
            {
                // Add current chunk
                chunks.Add(string.Join(" ", currentChunk));
                
                // Start new chunk with overlap
                if (overlap > 0 && currentChunk.Count > 1)
                {
                    var overlapSentences = currentChunk.TakeLast(Math.Min(2, currentChunk.Count - 1)).ToList();
                    currentChunk = overlapSentences;
                    currentLength = string.Join(" ", overlapSentences).Length;
                }
                else
                {
                    currentChunk.Clear();
                    currentLength = 0;
                }
            }

            currentChunk.Add(sentenceWithPeriod);
            currentLength += sentenceWithPeriod.Length + 1; // +1 for space
        }

        // Add the last chunk if it has content
        if (currentChunk.Count > 0)
        {
            chunks.Add(string.Join(" ", currentChunk));
        }

        return chunks;
    }
}
