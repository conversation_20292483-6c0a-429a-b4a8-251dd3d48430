namespace ChatbotApp.Services;

using ChatbotApp.Models;

public interface IProcedureVectorService
{
    Task<bool> IndexProcedureAsync(ProcedureVectorItem procedure);
    Task<bool> IndexAllProceduresAsync();
    Task<List<ProcedureVector>> SearchProceduresAsync(string query, int topK = 5, float minScore = 0.7f);
    Task<List<ProcedureVector>> SearchProceduresAsync11(string query, int topK = 5, float minScore = 0.7f);

    Task<bool> DeleteProcedureAsync(string procedureId);
    Task<bool> IsAvailableAsync();
}


