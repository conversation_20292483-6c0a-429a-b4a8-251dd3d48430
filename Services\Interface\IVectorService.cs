using ChatbotApp.Models;

namespace ChatbotApp.Services.Interfaces;

public interface IVectorService
{
    Task InitializeAsync();
    Task<bool> IndexDocumentAsync(DocumentInfo document);
    Task<List<DocumentSearchResult>> SearchAsync(string query, int topK = 5, float minScore = 0.7f);
    Task<bool> DeleteDocumentAsync(string documentId);
    Task<bool> IsAvailableAsync();
    Task<float[]> GenerateEmbeddingAsync(string text);
}
