using ChatbotApp.Hubs;
using ChatbotApp.Models;
using ChatbotApp.Services;
using ChatbotApp.Services.Interfaces;
using Microsoft.SemanticKernel;
using Qdrant.Client;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();

// Register Ollama service with Semantic Kernel
builder.Services.AddScoped<IOllamaService, OllamaService>();
builder.Services.AddScoped<IProcedureVectorService, ProcedureVectorService>();


builder.Services.AddScoped<Kernel>(sp =>
{
    var logger = sp.GetRequiredService<ILogger<OllamaService>>();

    var kernelBuilder = Kernel.CreateBuilder();

    kernelBuilder.AddOllamaChatCompletion("qwen3:14b", new Uri("http://localhost:11434"));
    kernelBuilder.AddOllamaEmbeddingGenerator("nomic-embed-text", new Uri("http://localhost:11434"));
    kernelBuilder.Services.AddQdrantVectorStore();

    kernelBuilder.Services.AddSingleton(_ => new QdrantClient(new Uri("http://localhost:6333")));

    return kernelBuilder.Build();
});

// Register document processing services
builder.Services.AddScoped<IVectorService, QdrantVectorService>();
builder.Services.AddScoped<IDocumentProcessingService, DocumentProcessingService>();

// Add logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

// Map controllers
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Map SignalR hub
app.MapHub<ChatHub>("/chatHub");

app.Run();
