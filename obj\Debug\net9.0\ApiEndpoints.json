[{"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "GetDocument", "RelativePath": "api/Document/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "DeleteDocument", "RelativePath": "api/Document/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "GetDocuments", "RelativePath": "api/Document/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "ProcessExistingFiles", "RelativePath": "api/Document/process-existing", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "SearchDocuments", "RelativePath": "api/Document/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChatbotApp.Models.DocumentSearchRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "GetStatus", "RelativePath": "api/Document/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ChatbotApp.Controllers.DocumentController", "Method": "UploadDocument", "RelativePath": "api/Document/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}]