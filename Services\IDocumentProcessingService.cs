using ChatbotApp.Models;

namespace ChatbotApp.Services;

public interface IDocumentProcessingService
{
    Task<DocumentProcessingResult> ProcessDocumentAsync(string filePath);
    Task<DocumentProcessingResult> ProcessDocumentAsync(Stream fileStream, string fileName);
    Task<List<DocumentInfo>> GetAllDocumentsAsync();
    Task<DocumentInfo?> GetDocumentByIdAsync(string documentId);
    Task<bool> DeleteDocumentAsync(string documentId);
}
