{"GlobalPropertiesHash": "4kiAcxH2iLT5wmBFKttoj5jiYaK8I2pTP4Co7TC4Zdg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["NrJKoPehi8dI4PP/IqbqZa446OSV7XqHbgk0ApgnPNw=", "aW6Dpw25Jfm0anI90kyEe8RJp583g/tVAEaptsbLvyw=", "V6AsGZPPTs52IbHrXHGRKmwnuWlG/nyI8S1gELTWHK4=", "i9s4fhgnfb3Ab801AJDlschmAM3F+YC5T9MZ4HDTkiw="], "CachedAssets": {"NrJKoPehi8dI4PP/IqbqZa446OSV7XqHbgk0ApgnPNw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04.2308706+00:00"}}, "CachedCopyCandidates": {}}