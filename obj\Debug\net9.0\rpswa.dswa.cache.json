{"GlobalPropertiesHash": "4kiAcxH2iLT5wmBFKttoj5jiYaK8I2pTP4Co7TC4Zdg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["sY6iBRJOXrMSBrjsTAb4HSPTB022Ws1e/tT4QI4bwKs=", "T7srzZxSSRyYmzV321Gbmuj0N6oC6kUUKoccHRsvdLU=", "bZHLf7MWFbmnaN28rNB+g/m0/aUGqffC4PHQsiISkwM=", "CVZzu+Rb0jIP4PdCmOtFcR8tWB4HEP9kxRwgXFp1YA4=", "ndMnCtxQxWWWhJLFZHAj3KqA12ezb7ZZHywsLNJCgFA=", "l+OQjW6JcB/xLcmJpCWOGEmgto3eH8GjksyMF/RcCy8=", "S2LLaWRLJ1naxSu/7BfCh98QpZcM9vs98RDwv5g7eZ8=", "W72b/GUCqG/wtA6uTU0aG8R/ILhMwLreSwRQz/i11Ac=", "gsWCKF7mrQ9IL4ORMKP+Dy77C76GQuJjSk0XGiWQNEQ=", "TdpIYWU+Sn8LrX05NRumWXdtXobJ3ldsaI9+ujPAX9c=", "rRrSEim11r+g0wkqwAXqcHdqZbbL2U+w5e2yYM8hp38=", "IcgIchD2jnYqDtYTWV4uGwZRTxllkgRwwUFa9PZ5DhA=", "9+gSJeAelCERqsBT2MFO72iNDGdSVBlm/SD0fFmW600=", "a6hV/p9N5k+wzT8Od5FTNiBLsj/Vdg8G0d2DaNO2u4c=", "eDIySlnu8HYG2caxib+1HyFnMo4X99JIoK9LE46TToM="], "CachedAssets": {"bZHLf7MWFbmnaN28rNB+g/m0/aUGqffC4PHQsiISkwM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "files/PR_1#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "14askf0i1a", "Integrity": "zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\files\\PR_1.docx", "FileLength": 140853, "LastWriteTime": "2025-07-17T18:48:18.187018+00:00"}, "CVZzu+Rb0jIP4PdCmOtFcR8tWB4HEP9kxRwgXFp1YA4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04.2308706+00:00"}, "ndMnCtxQxWWWhJLFZHAj3KqA12ezb7ZZHywsLNJCgFA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "awmycvggfz", "Integrity": "lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\documents.js", "FileLength": 9158, "LastWriteTime": "2025-07-17T21:09:36.3050802+00:00"}, "sY6iBRJOXrMSBrjsTAb4HSPTB022Ws1e/tT4QI4bwKs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\FIN-03.json", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/FIN-03#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wi72cewi0b", "Integrity": "aGknbdXUMyWqvi5FEufOvVhpCEQ7yPggaUccCns2Gqg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\data\\FIN-03.json", "FileLength": 3709, "LastWriteTime": "2025-07-18T18:01:34.6112312+00:00"}, "T7srzZxSSRyYmzV321Gbmuj0N6oC6kUUKoccHRsvdLU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\data\\HR-07.json", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "data/HR-07#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "if7imjr4iq", "Integrity": "HqiU2N+dHDxfQDY4tCeNmpedToz2HOq1z09TUpxZ/WI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\data\\HR-07.json", "FileLength": 4006, "LastWriteTime": "2025-07-18T18:01:11.9855493+00:00"}}, "CachedCopyCandidates": {}}