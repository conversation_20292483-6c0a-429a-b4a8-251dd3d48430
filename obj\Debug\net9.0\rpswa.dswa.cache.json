{"GlobalPropertiesHash": "4kiAcxH2iLT5wmBFKttoj5jiYaK8I2pTP4Co7TC4Zdg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["bZHLf7MWFbmnaN28rNB+g/m0/aUGqffC4PHQsiISkwM=", "CVZzu+Rb0jIP4PdCmOtFcR8tWB4HEP9kxRwgXFp1YA4=", "ndMnCtxQxWWWhJLFZHAj3KqA12ezb7ZZHywsLNJCgFA=", "l+OQjW6JcB/xLcmJpCWOGEmgto3eH8GjksyMF/RcCy8=", "S2LLaWRLJ1naxSu/7BfCh98QpZcM9vs98RDwv5g7eZ8=", "W72b/GUCqG/wtA6uTU0aG8R/ILhMwLreSwRQz/i11Ac=", "gsWCKF7mrQ9IL4ORMKP+Dy77C76GQuJjSk0XGiWQNEQ=", "TdpIYWU+Sn8LrX05NRumWXdtXobJ3ldsaI9+ujPAX9c=", "rRrSEim11r+g0wkqwAXqcHdqZbbL2U+w5e2yYM8hp38=", "IcgIchD2jnYqDtYTWV4uGwZRTxllkgRwwUFa9PZ5DhA=", "eDIySlnu8HYG2caxib+1HyFnMo4X99JIoK9LE46TToM="], "CachedAssets": {"bZHLf7MWFbmnaN28rNB+g/m0/aUGqffC4PHQsiISkwM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\files\\PR_1.docx", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "files/PR_1#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "14askf0i1a", "Integrity": "zy3SfLuAa5vEWD0sg/Z5TElG5F+VWKAJ09Dr4Sxzvt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\files\\PR_1.docx", "FileLength": 140853, "LastWriteTime": "2025-07-17T18:48:18.187018+00:00"}, "CVZzu+Rb0jIP4PdCmOtFcR8tWB4HEP9kxRwgXFp1YA4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\chat.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/chat#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5g4imy9nsy", "Integrity": "BdUN/wj+5t11V7wS5w6UOSYoa1ZShQGKsYV3cphFkzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\chat.js", "FileLength": 4694, "LastWriteTime": "2025-07-17T19:58:04.2308706+00:00"}, "ndMnCtxQxWWWhJLFZHAj3KqA12ezb7ZZHywsLNJCgFA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\js\\documents.js", "SourceId": "ChatbotApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\AspNetChatBot\\wwwroot\\", "BasePath": "_content/ChatbotApp", "RelativePath": "js/documents#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "awmycvggfz", "Integrity": "lu/pLh+6W5OMnkpSA3Q1TfHxrAytVWyoZ4R77nR3LI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\documents.js", "FileLength": 9158, "LastWriteTime": "2025-07-17T21:09:36.3050802+00:00"}}, "CachedCopyCandidates": {}}