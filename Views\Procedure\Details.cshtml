@model ChatbotApp.Models.ProcedureVectorItem

@{
    ViewData["Title"] = "Procedure Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-file-alt me-2"></i>
                    Procedure Details: @Model.Id
                </h2>
                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>
                        Edit
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to List
                    </a>
                </div>
            </div>

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">ID:</dt>
                                <dd class="col-sm-8"><span class="badge bg-primary fs-6">@Model.Id</span></dd>
                                
                                <dt class="col-sm-4">Version:</dt>
                                <dd class="col-sm-8"><span class="badge bg-info fs-6">@Model.Payload.Version</span></dd>
                                
                                <dt class="col-sm-4">Sector:</dt>
                                <dd class="col-sm-8">@Model.Payload.Sector</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Department:</dt>
                                <dd class="col-sm-8">@Model.Payload.Department</dd>
                                
                                <dt class="col-sm-4">Section:</dt>
                                <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.Payload.Section) ? "N/A" : Model.Payload.Section)</dd>
                                
                                <dt class="col-sm-4">Title:</dt>
                                <dd class="col-sm-8"><strong>@Model.Payload.Title</strong></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Procedure Details -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Procedure Content
                    </h5>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.Payload.Objective))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-bullseye me-2"></i>Objective</h6>
                            <p class="ms-3">@Model.Payload.Objective</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Scope))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-crosshairs me-2"></i>Scope</h6>
                            <p class="ms-3">@Model.Payload.Scope</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Policy))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-gavel me-2"></i>Policy</h6>
                            <p class="ms-3">@Model.Payload.Policy</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Responsibility))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-user-tie me-2"></i>Responsibility</h6>
                            <p class="ms-3">@Model.Payload.Responsibility</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Definitions))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-book me-2"></i>Definitions</h6>
                            <p class="ms-3">@Model.Payload.Definitions</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Payload.Description))
                    {
                        <div class="mb-4">
                            <h6 class="text-primary"><i class="fas fa-align-left me-2"></i>Description</h6>
                            <p class="ms-3">@Model.Payload.Description</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Flow Steps -->
            @if (Model.Payload.FlowSteps.Any())
            {
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list-ol me-2"></i>
                            Flow Steps
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Step</th>
                                        <th>Action</th>
                                        <th>Responsible</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var step in Model.Payload.FlowSteps.OrderBy(s => s.Step))
                                    {
                                        <tr>
                                            <td><span class="badge bg-primary">@step.Step</span></td>
                                            <td>@step.Action</td>
                                            <td>@step.Responsible</td>
                                            <td>@step.Duration</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- RACI Matrix -->
            @if (Model.Payload.Raci.Any())
            {
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            RACI Matrix
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Task</th>
                                        <th>Responsible</th>
                                        <th>Accountable</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var raci in Model.Payload.Raci)
                                    {
                                        <tr>
                                            <td>@raci.Task</td>
                                            <td>@raci.Responsible</td>
                                            <td>@raci.Accountable</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- KPIs -->
            @if (Model.Payload.Kpis.Any())
            {
                <div class="card mt-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Key Performance Indicators (KPIs)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var kpi in Model.Payload.Kpis)
                                    {
                                        <tr>
                                            <td>@kpi.Name</td>
                                            <td><span class="badge bg-info">@kpi.Type</span></td>
                                            <td>@kpi.Value</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- Forms -->
            @if (Model.Payload.Forms.Any())
            {
                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-invoice me-2"></i>
                            Related Forms
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Name</th>
                                        <th>Code</th>
                                        <th>Retention</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var form in Model.Payload.Forms)
                                    {
                                        <tr>
                                            <td>@form.Name</td>
                                            <td><span class="badge bg-secondary">@form.Code</span></td>
                                            <td>@form.Retention</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- References -->
            @if (Model.Payload.References.Any())
            {
                <div class="card mt-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-link me-2"></i>
                            References
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            @foreach (var reference in Model.Payload.References)
                            {
                                <li class="list-group-item">
                                    <i class="fas fa-external-link-alt me-2 text-primary"></i>
                                    @reference
                                </li>
                            }
                        </ul>
                    </div>
                </div>
            }

            <!-- Action Buttons -->
            <div class="mt-4 mb-4">
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-lg me-3">
                    <i class="fas fa-edit me-2"></i>
                    Edit Procedure
                </a>
                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger btn-lg me-3">
                    <i class="fas fa-trash me-2"></i>
                    Delete Procedure
                </a>
                <a asp-action="Index" class="btn btn-secondary btn-lg">
                    <i class="fas fa-list me-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
